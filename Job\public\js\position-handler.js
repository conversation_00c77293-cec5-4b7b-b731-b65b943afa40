class PositionHandler {
    constructor() {
        // Get DOM elements
        this.positionSelect = document.getElementById('position');
        this.positionsList = document.getElementById('positionsList');
        this.positionSearch = document.getElementById('positionSearch');
        this.sectionInput = document.getElementById('section');
        this.sectionIdInput = document.getElementById('sectionId');
        this.departmentInput = document.getElementById('department');
        this.departmentIdInput = document.getElementById('departmentId');
        
        // Check if required elements exist
        if (!this.positionSelect || !this.positionsList) {
            console.warn('Position handler initialized with missing required elements');
            // Don't initialize further if essential elements are missing
            return;
        }
        
        this.positions = [];
        this.selectedPosition = null;
        
        this.init();
        this.loadPositions();
    }

    init() {
        if (this.positionSearch) {
            this.positionSearch.addEventListener('input', () => {
                this.filterPositions();
                this.toggleSearchClear();
            });

            // Add search clear functionality
            const searchClear = document.querySelector('.search-clear');
            if (searchClear) {
                searchClear.addEventListener('click', () => {
                    this.positionSearch.value = '';
                    this.filterPositions();
                    this.toggleSearchClear();
                    this.positionSearch.focus();
                });
            }
        }
    }

    toggleSearchClear() {
        const searchClear = document.querySelector('.search-clear');
        if (searchClear) {
            searchClear.style.display = this.positionSearch.value ? 'block' : 'none';
        }
    }

    async loadPositions() {
        try {
            const response = await fetch('/api/positions');
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            
            const positions = await response.json();
            this.positions = positions;
            
            // Initial render of positions
            this.filterPositions();
            
        } catch (error) {
            console.error('Error loading positions:', error);
            this.showError('Error loading positions. Please try again.');
        }
    }

    filterPositions() {
        // Safely handle null positionSearch element
        const searchTerm = this.positionSearch && this.positionSearch.value ? 
            this.positionSearch.value.toLowerCase() : '';
        
        const filteredPositions = this.positions.filter(pos => {
            return pos.PositionName.toLowerCase().includes(searchTerm) ||
                   (pos.DepartmentName && pos.DepartmentName.toLowerCase().includes(searchTerm)) ||
                   (pos.SECTIONName && pos.SECTIONName.toLowerCase().includes(searchTerm));
        });
        
        // Only proceed if positionsList exists
        if (this.positionsList) {
            this.renderPositions(filteredPositions);
            this.updateSearchStatus(filteredPositions.length, this.positions.length);
        }
    }

    renderPositions(positions) {
        this.positionsList.innerHTML = '';
        
        if (positions.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'no-results';
            noResults.innerHTML = document.dir === 'rtl'
                ? '<i class="fas fa-search"></i> لا توجد وظائف متطابقة'
                : '<i class="fas fa-search"></i> No matching positions found';
            this.positionsList.appendChild(noResults);
            return;
        }
        
        positions.forEach(pos => {
            const positionItem = document.createElement('div');
            positionItem.className = 'position-item';
            if (this.selectedPosition && this.selectedPosition.HIMS_ID === pos.HIMS_ID) {
                positionItem.classList.add('selected');
            }
            
            positionItem.innerHTML = `
                <div class="position-info">
                    <div class="position-title">${this.highlightMatch(pos.PositionName)}</div>
                    <div class="position-details">
                        <i class="fas fa-building"></i> ${this.highlightMatch(pos.DepartmentName)}
                        <i class="fas fa-sitemap ml-2"></i> ${this.highlightMatch(pos.SECTIONName)}
                    </div>
                </div>
            `;
            
            positionItem.addEventListener('click', () => this.selectPosition(pos));
            this.positionsList.appendChild(positionItem);
        });
    }

    highlightMatch(text) {
        if (!text || !this.positionSearch.value) return text;
        
        const searchTerm = this.positionSearch.value.toLowerCase();
        const index = text.toLowerCase().indexOf(searchTerm);
        
        if (index >= 0) {
            return text.substring(0, index) +
                   `<span class="highlight">${text.substring(index, index + searchTerm.length)}</span>` +
                   text.substring(index + searchTerm.length);
        }
        
        return text;
    }

    selectPosition(position) {
        this.selectedPosition = position;
        
        // Update hidden select for form submission
        const option = document.createElement('option');
        option.value = position.HIMS_ID;
        option.textContent = position.PositionName;
        option.selected = true;
        this.positionSelect.innerHTML = '';
        this.positionSelect.appendChild(option);
        
        // Update section and department fields
        this.sectionInput.value = position.SECTIONName;
        this.sectionIdInput.value = position.SECTIONId;
        this.departmentInput.value = position.DepartmentName;
        this.departmentIdInput.value = position.DepartmentId;
        
        // Update validation states
        this.updateValidation(this.positionSelect, true);
        this.updateValidation(this.sectionInput, true);
        this.updateValidation(this.departmentInput, true);
        
        // Re-render positions to update selection visual
        this.filterPositions();
    }

    updateSearchStatus(visibleCount, totalCount) {
        const searchStatus = document.getElementById('searchStatus');
        if (!searchStatus) return;

        const isArabic = document.dir === 'rtl';
        if (this.positionSearch.value) {
            searchStatus.textContent = isArabic
                ? `عرض ${visibleCount} من ${totalCount} وظيفة`
                : `Showing ${visibleCount} of ${totalCount} positions`;
        } else {
            searchStatus.textContent = isArabic
                ? `${totalCount} وظيفة متاحة`
                : `${totalCount} positions available`;
        }
    }

    updateValidation(element, isValid = null) {
        if (!element) return;
        
        if (isValid === null) {
            isValid = element.value && !element.disabled;
        }
        
        if (isValid) {
            element.classList.add('is-valid');
            element.classList.remove('is-invalid');
        } else {
            element.classList.add('is-invalid');
            element.classList.remove('is-valid');
        }
    }

    showError(message) {
        // Only proceed if positionsList exists
        if (!this.positionsList) {
            console.error('Error showing error message: positionsList element not found');
            return;
        }
        
        this.positionsList.innerHTML = `
            <div class="no-results" style="color: #dc3545;">
                <i class="fas fa-exclamation-circle"></i> ${message}
            </div>
        `;
    }

    gatherFormData() {
        const firstName = document.querySelector('input[name="firstName"]').value || '';
        const secondName = document.querySelector('input[name="secondName"]').value || '';
        const thirdName = document.querySelector('input[name="thirdName"]').value || '';
        const fullName = [firstName, secondName, thirdName].filter(Boolean).join(' ');

        // Generate UID from National ID
        const nationalId = document.getElementById('nationalId').value;
        const uid = nationalId ? parseInt(nationalId.slice(-6)) : null;

        // Get educational data
        const specializationSelect = document.getElementById('specialization');
        const universitySelect = document.getElementById('university');
        const facultySelect = document.getElementById('faculty');
        const gradeSelect = document.getElementById('grade');
        const yearOfGraduation = document.getElementById('yearOfGraduation');

        return {
            Name: fullName,
            Main_Position: parseInt(this.departmentIdInput.value) || null,
            SECTIONId: parseInt(this.sectionIdInput.value) || null,
            Sub_Position: this.selectedPosition ? this.selectedPosition.PositionName : '',
            Expected_Salary: parseFloat(document.getElementById('expectedSalary').value) || 0,
            National_ID: nationalId,
            Age: parseInt(document.getElementById('age').value) || 0,
            Marital_Status: document.getElementById('socialStatus').value,
            Current_Address: document.getElementById('address').value,
            Mobile: document.getElementById('phoneNumber').value,
            Email: document.getElementById('email').value,
            Military_Status: document.getElementById('conscriptionStatus').value,
            Religion: document.getElementById('religion').value,
            Gender: document.getElementById('gender').value,
            HIMS_ID: this.selectedPosition ? parseInt(this.selectedPosition.HIMS_ID) : null,
            UID: uid,
            // Add educational data
            Grade_ID: parseInt(specializationSelect.value) || 0,
            Category_ID: parseInt(specializationSelect.options[specializationSelect.selectedIndex]?.dataset.categoryId) || 0,
            FID: parseInt(facultySelect.value) || 0,
            Grade: gradeSelect.value || '',
            Grad_Year: yearOfGraduation.value || '',
            uniname_ar: universitySelect.options[universitySelect.selectedIndex]?.text || '',
            Faculty: facultySelect.options[facultySelect.selectedIndex]?.text || '',
            CategoryName_AR: specializationSelect.options[specializationSelect.selectedIndex]?.text || '',
            GradeName_AR: specializationSelect.options[specializationSelect.selectedIndex]?.dataset.gradeName || '',
            // Add experience data
            Current_Employer: document.getElementById('currentEmployer').value || '',
            Job_Title: document.getElementById('jobTitle').value || '',
            EXP_Years_From: document.getElementById('from').value || null,
            EXP_Years_To: document.getElementById('to').value || null,
            Current_Salary: parseFloat(document.getElementById('currentSalary').value) || 0,
            Leave_Reason: document.getElementById('reasonForLeaving').value || '',
            Hear_About_Us: document.getElementById('hearAboutUs').value || '',
            // Add other information
            EMP_ENG_LVL: document.getElementById('englishLevel').value || '',
            EMP_PC_LVL: document.getElementById('computerSkills').value || '',
            Has_Relative_In_Hospital: document.getElementById('workAtShifa').value || 'No',
            Worked_At_Shifa_Before: document.getElementById('workAtShifa').value || 'No',
            Has_Chronic_Disease: 'No' // Default value since it's not in the form
        };
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.positionHandler = new PositionHandler();
});