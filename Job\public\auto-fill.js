(function() {
    console.log("Auto-fill script loaded");
    
    // Function to get URL parameters
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        const results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }
    
    // Function to check if an element exists
    function elementExists(selector) {
        return document.querySelector(selector) !== null;
    }
    
    // Function to fill in a form field
    function fillField(selector, value) {
        if (!value) return false;
        
        const field = document.querySelector(selector);
        if (!field) {
            console.error(`Field not found: ${selector}`);
            return false;
        }
        
        field.value = value;
        
        // Trigger change event
        const event = new Event('change', { bubbles: true });
        field.dispatchEvent(event);
        
        // Add validation classes
        field.classList.add('is-valid');
        field.classList.remove('is-invalid');
        
        return true;
    }
    
    // Function to wait for an element to appear
    function waitForElement(selector, callback, maxWaitTime = 10000) {
        if (elementExists(selector)) {
            callback();
            return;
        }
        
        let waited = 0;
        const interval = 100;
        
        const checkInterval = setInterval(() => {
            waited += interval;
            
            if (elementExists(selector)) {
                clearInterval(checkInterval);
                callback();
                return;
            }
            
            if (waited >= maxWaitTime) {
                console.error(`Timeout waiting for element: ${selector}`);
                clearInterval(checkInterval);
            }
        }, interval);
    }
    
    // Load the position selector script if not already loaded
    function loadPositionSelector() {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            if (window.selectPositionByText) {
                console.log("Position selector already loaded");
                resolve();
                return;
            }
            
            console.log("Loading position selector script");
            const script = document.createElement('script');
            script.src = '/position-selector.js';
            script.onload = () => {
                console.log("Position selector script loaded successfully");
                resolve();
            };
            script.onerror = (err) => {
                console.error("Failed to load position selector script", err);
                reject(err);
            };
            document.head.appendChild(script);
        });
    }
    
    // Select a position from the list
    function selectPosition(positionText) {
        if (!positionText) {
            console.error("No position text provided");
            return false;
        }
        
        console.log(`Selecting position: ${positionText}`);
        
        // Ensure position selector is loaded
        if (!window.selectPositionByText) {
            console.error("Position selector not available");
            loadPositionSelector().then(() => {
                if (window.selectPositionByText) {
                    window.selectPositionByText(positionText);
                }
            });
            return false;
        }
        
        return window.selectPositionByText(positionText);
    }
    
    // Main initialization function
    function initialize() {
        console.log("Initializing auto-fill");
        
        // Load position selector script
        loadPositionSelector().catch(err => {
            console.warn("Could not load position selector, will try again later", err);
        });
        
        // Check for token in URL
        const token = getUrlParameter('token');
        
        if (token) {
            console.log("Token found in URL:", token);
            
            // Set token in localStorage if needed
            if (window.localStorage) {
                window.localStorage.setItem('completionToken', token);
                console.log("Token saved to localStorage");
            }
            
            // Check for position parameter
            const position = getUrlParameter('position');
            
            // Wait for required fields to be available
            waitForElement('.position-container', () => {
                // Set default position if not already set
                setTimeout(() => {
                    // Check if there's already a selected position
                    if (!document.querySelector('.position-container.selected')) {
                        if (position) {
                            console.log(`Selecting specified position: ${position}`);
                            selectPosition(position);
                        } else {
                            console.log("No position selected, selecting default position");
                            // Try to select Software Specialist position
                            selectPosition('اخصائي برمجيات');
                        }
                    } else {
                        console.log("Position already selected");
                    }
                }, 1000);
            });
        } else {
            console.log("No token found in URL");
            
            // Check localStorage for token
            if (window.localStorage) {
                const savedToken = window.localStorage.getItem('completionToken');
                
                if (savedToken) {
                    console.log("Token found in localStorage:", savedToken);
                }
            }
            
            // Still wait for position container and try to select position
            waitForElement('.position-container', () => {
                setTimeout(() => {
                    if (!document.querySelector('.position-container.selected')) {
                        console.log("No position selected, selecting default position");
                        selectPosition('اخصائي برمجيات');
                    }
                }, 1000);
            });
        }
    }
    
    // When the document is fully loaded, initialize
    if (document.readyState === 'complete') {
        initialize();
    } else {
        window.addEventListener('load', initialize);
    }
})(); 