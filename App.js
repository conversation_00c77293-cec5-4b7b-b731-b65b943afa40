import express from 'express';
import bodyParser from 'body-parser';
import path from 'path';
import sql from 'mssql';
import nodemailer from 'nodemailer';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import dotenv from 'dotenv';
import fetch from 'node-fetch';
import fs from 'fs';
import fileUpload from 'express-fileupload';

// Initialize dotenv
dotenv.config();

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();

// SQL Server configuration
const config = {
    user: 'mirth',
    password: '$hif@Egypt',
    server: 'localhost',
    database: 'SHMS',
    options: {
        encrypt: false
    }
};

// Add email transporter configuration
const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false,
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
    },
    tls: {
        rejectUnauthorized: false
    }
});

// Test email configuration
transporter.verify(function(error, success) {
    if (error) {
        console.log('Email server connection error:', error);
    } else {
        console.log('Email server connection successful');
    }
});

// Set up middleware
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'job', 'public')));
app.use(fileUpload());

// Add this middleware after the fileUpload middleware
app.use((req, res, next) => {
    // Detect mobile devices
    const userAgent = req.headers['user-agent'] || '';
    req.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

    // Add request tracking for mobile submissions
    if (req.isMobile) {
        console.log(`Mobile request detected: ${userAgent}`);

        // Add longer timeout for mobile submissions
        req.setTimeout(120000); // 2 minutes
    }

    next();
});

// Set EJS as templating engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'job', 'views'));

// ===== DATA MAPPING AND VALIDATION FUNCTIONS =====

/**
 * Validates form data according to database constraints
 * @param {Object} formData - The form data to validate
 * @returns {Object} - Validation result with success flag and errors array
 */
function validateFormData(formData) {
    const errors = [];

    // Required fields validation - Updated to match frontend field names
    const requiredFields = [
        { field: 'firstName', message: 'First name is required' },
        { field: 'secondName', message: 'Second name is required' },
        { field: 'thirdName', message: 'Third name is required' },
        { field: 'Mobile', message: 'Phone number is required' },
        { field: 'National_ID', message: 'National ID is required' },
        { field: 'Age', message: 'Age is required' },
        { field: 'Gender', message: 'Gender is required' },
        { field: 'Religion', message: 'Religion is required' },
        { field: 'Social_Status', message: 'Marital status is required' }, // Maps to Marital_Status in DB
        { field: 'City', message: 'City is required' },
        { field: 'Address', message: 'Address is required' }, // Maps to Current_Address in DB
        { field: 'Scientific_Degree', message: 'Scientific degree is required' },
        { field: 'Specialization', message: 'Specialization is required' },
        { field: 'University', message: 'University is required' },
        { field: 'Faculty', message: 'Faculty is required' },
        { field: 'Grade', message: 'Grade is required' },
        { field: 'Year_Of_Graduation', message: 'Year of graduation is required' },
        { field: 'Sub_Position', message: 'Position is required' },
        { field: 'Expected_Salary', message: 'Expected salary is required' },
        { field: 'English_Level', message: 'English level is required' },
        { field: 'Computer_Skills', message: 'Computer skills level is required' },
        { field: 'Work_At_Shifa', message: 'Previous work at Shifa selection is required' },
        { field: 'Chronic_Diseases', message: 'Chronic diseases selection is required' },
        { field: 'Relative_In_Hospital', message: 'Relatives in hospital selection is required' }
    ];

    // Check required fields
    requiredFields.forEach(({ field, message }) => {
        if (!formData[field] || formData[field].toString().trim() === '') {
            errors.push(message);
        }
    });

    // National ID validation - Updated field names
    if (formData.National_ID) {
        if (formData.ID_Type === 'national') {
            // Egyptian National ID: 14 digits
            if (!/^\d{14}$/.test(formData.National_ID)) {
                errors.push('National ID must be exactly 14 digits');
            }
        } else if (formData.ID_Type === 'passport') {
            // Passport: 6-20 alphanumeric characters
            if (!/^[A-Za-z0-9]{6,20}$/.test(formData.National_ID)) {
                errors.push('Passport number must be 6-20 alphanumeric characters');
            }
        }
    }

    // Mobile number validation (Egyptian format) - Updated field name
    if (formData.Mobile) {
        if (!/^01[0125][0-9]{8}$/.test(formData.Mobile)) {
            errors.push('Phone number must be valid Egyptian mobile format (01XXXXXXXXX)');
        }
    }

    // Email validation - Updated field name
    if (formData.Email && formData.Email.trim() !== '') {
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.Email)) {
            errors.push('Email must be in valid format');
        }
    }

    // Age validation - Handle both Age and age fields
    const ageValue = formData.Age || formData.age;
    if (ageValue) {
        const age = parseInt(ageValue);
        if (isNaN(age) || age < 16 || age > 70) {
            errors.push('Age must be between 16 and 70');
        }
    }

    // Year of graduation validation - Updated field name
    if (formData.Year_Of_Graduation) {
        const year = parseInt(formData.Year_Of_Graduation);
        const currentYear = new Date().getFullYear();
        if (isNaN(year) || year < 1950 || year > currentYear) {
            errors.push(`Year of graduation must be between 1950 and ${currentYear}`);
        }
    }

    // Salary validation - Updated field names
    if (formData.Expected_Salary) {
        const salary = parseFloat(formData.Expected_Salary);
        if (isNaN(salary) || salary <= 0) {
            errors.push('Expected salary must be a positive number');
        }
    }

    if (formData.Current_Salary) {
        const salary = parseFloat(formData.Current_Salary);
        if (isNaN(salary) || salary <= 0) {
            errors.push('Current salary must be a positive number');
        }
    }

    return {
        success: errors.length === 0,
        errors: errors
    };
}

/**
 * Resolves display names to database IDs
 * @param {Object} pool - Database connection pool
 * @param {Object} formData - The form data containing display names
 * @returns {Object} - Object containing resolved IDs
 */
async function resolveFormDataIds(pool, formData) {
    const resolvedIds = {};

    try {
        // Resolve University ID - Frontend sends ID, we need to validate it exists and get name
        if (formData.University) {
            console.log(`🏫 Looking up University by ID: "${formData.University}"`);
            const universityQuery = `
                SELECT uni_id, uniname_ar, uniname_en
                FROM [SHMS].[dbo].[University]
                WHERE uni_id = @universityId
            `;
            const universityResult = await pool.request()
                .input('universityId', sql.Int, parseInt(formData.University))
                .query(universityQuery);

            console.log(`   Found ${universityResult.recordset.length} university records`);
            if (universityResult.recordset.length > 0) {
                resolvedIds.UID = universityResult.recordset[0].uni_id;
                resolvedIds.uniname_ar = universityResult.recordset[0].uniname_ar;
                console.log(`   ✅ University ID resolved: ${resolvedIds.UID} (${universityResult.recordset[0].uniname_ar})`);
            } else {
                console.log(`   ❌ University ID not found in database`);
                // Let's see what universities are available
                const allUniversitiesResult = await pool.request().query(`
                    SELECT TOP 5 uni_id, uniname_ar, uniname_en
                    FROM [SHMS].[dbo].[University]
                    ORDER BY uniname_ar
                `);
                console.log(`   Available universities (first 5):`);
                allUniversitiesResult.recordset.forEach(u => {
                    console.log(`     ID: ${u.uni_id}, AR: "${u.uniname_ar}", EN: "${u.uniname_en}"`);
                });
                throw new Error(`University ID not found: ${formData.University}`);
            }
        } else {
            console.log(`🏫 No university provided in form data`);
        }

        // Resolve Faculty ID - Frontend sends ID, we need to validate it exists and get name
        if (formData.Faculty) {
            console.log(`🏛️ Looking up Faculty by ID: "${formData.Faculty}"`);
            const facultyQuery = `
                SELECT fac_id, facname_ar, facname_en
                FROM [SHMS].[dbo].[Faculty]
                WHERE fac_id = @facultyId
            `;
            const facultyResult = await pool.request()
                .input('facultyId', sql.Int, parseInt(formData.Faculty))
                .query(facultyQuery);

            console.log(`   Found ${facultyResult.recordset.length} faculty records`);
            if (facultyResult.recordset.length > 0) {
                resolvedIds.FID = facultyResult.recordset[0].fac_id;
                resolvedIds.facname_ar = facultyResult.recordset[0].facname_ar;
                console.log(`   ✅ Faculty ID resolved: ${resolvedIds.FID} (${facultyResult.recordset[0].facname_ar})`);
            } else {
                console.log(`   ❌ Faculty ID not found in database`);
                // Let's see what faculties are available
                const allFacultiesResult = await pool.request().query(`
                    SELECT TOP 5 fac_id, facname_ar, facname_en
                    FROM [SHMS].[dbo].[Faculty]
                    ORDER BY facname_ar
                `);
                console.log(`   Available faculties (first 5):`);
                allFacultiesResult.recordset.forEach(f => {
                    console.log(`     ID: ${f.fac_id}, AR: "${f.facname_ar}", EN: "${f.facname_en}"`);
                });
                throw new Error(`Faculty ID not found: ${formData.Faculty}`);
            }
        } else {
            console.log(`🏛️ No faculty provided in form data`);
        }

        // Resolve Specialization Grade_ID and Category_ID - Frontend sends IDs
        if (formData.Specialization && formData.Scientific_Degree) {
            console.log(`🎓 Looking up Qualification by IDs: Grade_ID="${formData.Specialization}", Category_ID="${formData.Scientific_Degree}"`);
            const qualificationQuery = `
                SELECT Grade_ID, Category_ID, GradeName_AR, CategoryName_AR, GradeName_EN, CategoryName_EN
                FROM [SHMS].[dbo].[Qualifications]
                WHERE Grade_ID = @gradeId AND Category_ID = @categoryId
            `;
            const qualificationResult = await pool.request()
                .input('gradeId', sql.Int, parseInt(formData.Specialization))
                .input('categoryId', sql.Int, parseInt(formData.Scientific_Degree))
                .query(qualificationQuery);

            console.log(`   Found ${qualificationResult.recordset.length} qualification records`);
            if (qualificationResult.recordset.length > 0) {
                const qualification = qualificationResult.recordset[0];
                resolvedIds.Grade_ID = qualification.Grade_ID;
                resolvedIds.Category_ID = qualification.Category_ID;
                resolvedIds.GradeName_AR = qualification.GradeName_AR;
                resolvedIds.CategoryName_AR = qualification.CategoryName_AR;
                console.log(`   ✅ Qualification IDs resolved: Grade_ID=${resolvedIds.Grade_ID}, Category_ID=${resolvedIds.Category_ID}`);
                console.log(`   ✅ Names: ${qualification.GradeName_AR} in ${qualification.CategoryName_AR}`);
            } else {
                console.log(`   ❌ Qualification IDs not found in database`);
                // Let's see what qualifications are available for this category
                const categoryQualificationsResult = await pool.request()
                    .input('categoryId', sql.Int, parseInt(formData.Scientific_Degree))
                    .query(`
                        SELECT TOP 5 Grade_ID, Category_ID, GradeName_AR, CategoryName_AR, GradeName_EN, CategoryName_EN
                        FROM [SHMS].[dbo].[Qualifications]
                        WHERE Category_ID = @categoryId
                        ORDER BY GradeName_AR
                    `);
                console.log(`   Available qualifications for Category_ID "${formData.Scientific_Degree}" (first 5):`);
                categoryQualificationsResult.recordset.forEach(q => {
                    console.log(`     Grade_ID: ${q.Grade_ID}, AR: "${q.GradeName_AR}", EN: "${q.GradeName_EN}"`);
                });
                throw new Error(`Qualification IDs not found: Grade_ID=${formData.Specialization}, Category_ID=${formData.Scientific_Degree}`);
            }
        } else {
            console.log(`🎓 Missing specialization or scientific degree in form data`);
            console.log(`   Specialization: "${formData.Specialization}"`);
            console.log(`   Scientific Degree: "${formData.Scientific_Degree}"`);
        }

        // Resolve Position HIMS_ID and get Department/Section info - Frontend sends position name
        if (formData.Sub_Position) {
            console.log(`💼 Looking up Position: "${formData.Sub_Position}"`);
            const positionQuery = `
                SELECT JP.HIMS_ID, JP.DepartmentId, JP.SECTIONId, JP.PositionName
                FROM [SHMS].[dbo].[JobPositions] JP
                WHERE JP.PositionName = @positionName AND JP.IsActive = 1
            `;
            const positionResult = await pool.request()
                .input('positionName', sql.NVarChar, formData.Sub_Position)
                .query(positionQuery);

            console.log(`   Found ${positionResult.recordset.length} position records`);
            if (positionResult.recordset.length > 0) {
                const position = positionResult.recordset[0];
                resolvedIds.HIMS_ID = position.HIMS_ID;
                resolvedIds.Main_Position = position.DepartmentId;
                resolvedIds.SECTIONId = position.SECTIONId;
                resolvedIds.Sub_Position = position.PositionName;
                console.log(`   ✅ Position IDs resolved: HIMS_ID=${resolvedIds.HIMS_ID}, DepartmentId=${resolvedIds.Main_Position}, SECTIONId=${resolvedIds.SECTIONId}`);
            } else {
                console.log(`   ⚠️ Position not found in JobPositions table, using form data values`);
                // Use form data values if position not found in JobPositions table
                resolvedIds.HIMS_ID = null; // Will be NULL in database
                resolvedIds.Main_Position = formData.Main_Position ? parseInt(formData.Main_Position) : null;
                resolvedIds.SECTIONId = formData.SECTIONId ? parseInt(formData.SECTIONId) : null;
                resolvedIds.Sub_Position = formData.Sub_Position;
                console.log(`   ⚠️ Using form values: Main_Position=${resolvedIds.Main_Position}, SECTIONId=${resolvedIds.SECTIONId}, HIMS_ID=null`);

                // Let's see what positions are available for reference
                const allPositionsResult = await pool.request().query(`
                    SELECT TOP 5 HIMS_ID, PositionName, DepartmentId, SECTIONId
                    FROM [SHMS].[dbo].[JobPositions]
                    WHERE IsActive = 1
                    ORDER BY PositionName
                `);
                console.log(`   Available positions in JobPositions table (first 5):`);
                allPositionsResult.recordset.forEach(p => {
                    console.log(`     HIMS_ID: ${p.HIMS_ID}, Name: "${p.PositionName}", Dept: ${p.DepartmentId}, Section: ${p.SECTIONId}`);
                });
                // Don't throw error, continue with form data values
            }
        } else {
            console.log(`💼 No position provided in form data`);
            console.log(`   Sub_Position: "${formData.Sub_Position}"`);
            console.log(`   Main_Position: "${formData.Main_Position}"`);
            console.log(`   SECTIONId: "${formData.SECTIONId}"`);
            // Use form data values if available
            resolvedIds.Main_Position = formData.Main_Position ? parseInt(formData.Main_Position) : null;
            resolvedIds.SECTIONId = formData.SECTIONId ? parseInt(formData.SECTIONId) : null;
            resolvedIds.HIMS_ID = null;
        }

        console.log('\n🎯 FINAL RESOLVED IDs SUMMARY:');
        Object.entries(resolvedIds).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });

        return resolvedIds;

    } catch (error) {
        console.error('❌ ERROR in resolveFormDataIds:', error.message);
        console.error('Full error:', error);
        throw error;
    }
}

/**
 * Maps form data to database column structure
 * @param {Object} formData - The form data from the frontend
 * @param {Object} resolvedIds - The resolved database IDs
 * @returns {Object} - Mapped data ready for database insertion
 */
function mapFormDataToDatabase(formData, resolvedIds) {
    // Generate next candidate ID (this should be handled by database auto-increment in production)
    const currentDate = new Date().toISOString();

    // Combine name fields - Updated field names
    const fullName = `${formData.firstName} ${formData.secondName} ${formData.thirdName}`.trim();

    // Map form data to database columns - Updated to match frontend field names
    const mappedData = {
        // Auto-generated fields
        Application_Status: formData.Application_Status || 'New',
        Created_At: currentDate,
        Updated_At: currentDate,
        YearsOfExperience: 0, // Default value

        // Basic Information - Fixed field name mapping and proper trimming
        Name: fullName,
        National_ID: formData.ID_Type === 'national' ? formData.National_ID : null,
        Passport_Number: formData.ID_Type === 'passport' ? formData.National_ID : null,
        Mobile: formData.Mobile ? formData.Mobile.trim() : null,
        Email: formData.Email ? formData.Email.trim() : null,

        // Fix Age mapping - handle both Age and age fields
        Age: formData.Age ? parseInt(formData.Age) : (formData.age ? parseInt(formData.age) : null),

        Gender: formData.Gender ? formData.Gender.trim() : null,
        Religion: formData.Religion ? formData.Religion.trim() : null,

        // Fix Marital_Status mapping - frontend sends Social_Status
        Marital_Status: formData.Social_Status ? formData.Social_Status.trim() : null,

        // Fix Current_Address mapping - frontend sends Address
        Current_Address: formData.Address ? formData.Address.trim() : null,

        Military_Status: formData.Conscription_Status ? formData.Conscription_Status.trim() : null,

        // Education Information - Updated to use resolved data
        UID: resolvedIds.UID,
        Grade_ID: resolvedIds.Grade_ID,
        Category_ID: resolvedIds.Category_ID,
        FID: resolvedIds.FID,
        CategoryName_AR: resolvedIds.CategoryName_AR,
        GradeName_AR: resolvedIds.GradeName_AR,
        Grade: formData.Grade,
        Grad_Year: formData.Year_Of_Graduation,
        Faculty: resolvedIds.facname_ar || formData.Faculty,
        uniname_ar: resolvedIds.uniname_ar || formData.University,

        // Experience Information - Updated field names
        Current_Employer: formData.Current_Employer || null,
        Job_Title: formData.Job_Title || null,
        EXP_Years_From: formData.From_Date ? new Date(formData.From_Date) : null,
        EXP_Years_To: formData.To_Date ? new Date(formData.To_Date) : null,
        Current_Salary: formData.Current_Salary ? parseFloat(formData.Current_Salary) : null,
        Leave_Reason: formData.Reason_For_Leaving || null,
        Hear_About_Us: formData.Hear_About_Us || null,

        // Position Information - Updated field names with better fallback handling
        HIMS_ID: resolvedIds.HIMS_ID || null,
        Main_Position: formData.Main_Position || resolvedIds.Main_Position || null,
        SECTIONId: formData.SECTIONId || resolvedIds.SECTIONId || null,
        Sub_Position: formData.Sub_Position ? formData.Sub_Position.trim() : null,
        Expected_Salary: formData.Expected_Salary ? parseFloat(formData.Expected_Salary) : null,

        // Skills Information - Updated field names with trimming
        EMP_ENG_LVL: formData.English_Level ? formData.English_Level.trim() : null,
        EMP_PC_LVL: formData.Computer_Skills ? formData.Computer_Skills.trim() : null,

        // Additional Information - Updated field names with trimming
        Has_Relative_In_Hospital: formData.Relative_In_Hospital ? formData.Relative_In_Hospital.trim() : null,
        Worked_At_Shifa_Before: formData.Work_At_Shifa ? formData.Work_At_Shifa.trim() : null,
        Has_Chronic_Disease: formData.Chronic_Diseases ? formData.Chronic_Diseases.trim() : null,

        // File Information
        CV_Path: formData.CV_Path || formData.cvPath || null
    };

    return mappedData;
}

/**
 * Main function to process bilingual form submission
 * @param {Object} formData - The form data from the frontend
 * @returns {Object} - Processing result with success flag and data/errors
 */
async function processBilingualFormSubmission(formData) {
    let pool;
    try {
        // Step 1: Validate form data
        console.log('\n✅ STEP 3: VALIDATING FORM DATA...');
        console.log('Expected field names for validation (updated to match frontend):');
        const expectedFields = [
            'firstName', 'secondName', 'thirdName', 'Mobile', 'National_ID', 'Age', 'Gender',
            'Religion', 'Social_Status', 'City', 'Address', 'Scientific_Degree', 'Specialization',
            'University', 'Faculty', 'Grade', 'Year_Of_Graduation', 'Sub_Position', 'Expected_Salary',
            'English_Level', 'Computer_Skills', 'Work_At_Shifa', 'Chronic_Diseases', 'Relative_In_Hospital'
        ];

        console.log('🔍 CRITICAL FIELD MAPPING CHECK:');
        console.log(`   Age: "${formData.Age}" (should not be null/empty)`);
        console.log(`   Social_Status: "${formData.Social_Status}" (maps to Marital_Status in DB)`);
        console.log(`   Address: "${formData.Address}" (maps to Current_Address in DB)`);
        console.log(`   Gender: "${formData.Gender}" (should not be null/empty)`);
        console.log(`   Religion: "${formData.Religion}" (should not be null/empty)`);
        expectedFields.forEach(field => {
            const value = formData[field];
            const exists = value !== undefined && value !== null && value !== '';
            console.log(`  ${field}: ${exists ? '✓' : '✗'} "${value}"`);
        });

        const validation = validateFormData(formData);
        console.log('\n📋 VALIDATION RESULTS:');
        console.log('Success:', validation.success);
        if (!validation.success) {
            console.log('Validation errors:', validation.errors);
            return {
                success: false,
                errors: validation.errors,
                step: 'validation'
            };
        }
        console.log('✅ All validations passed!');

        // Step 2: Connect to database
        console.log('\n🔗 STEP 4: CONNECTING TO DATABASE...');
        pool = await sql.connect(config);
        console.log('✅ Database connection established');

        // Step 3: Resolve display names to database IDs
        console.log('\n🔍 STEP 5: RESOLVING FORM DATA IDs...');
        console.log('Looking up database IDs for:');
        console.log(`  University: "${formData.university}"`);
        console.log(`  Faculty: "${formData.faculty}"`);
        console.log(`  Scientific Degree: "${formData.scientificDegree}"`);
        console.log(`  Specialization: "${formData.specialization}"`);
        console.log(`  Position: "${formData.position}"`);

        const resolvedIds = await resolveFormDataIds(pool, formData);
        console.log('\n📊 RESOLVED IDs RESULTS:');
        Object.entries(resolvedIds).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });

        // Step 4: Map form data to database structure
        console.log('\n🗺️ STEP 6: MAPPING FORM DATA TO DATABASE STRUCTURE...');
        const mappedData = mapFormDataToDatabase(formData, resolvedIds);
        console.log('\n📋 FINAL MAPPED DATA FOR DATABASE:');
        Object.entries(mappedData).forEach(([key, value]) => {
            console.log(`  ${key}: ${value} (${typeof value})`);
        });

        // Step 5: Check for duplicate National ID (if not passport) - Updated field names
        if (formData.ID_Type === 'national' && formData.National_ID) {
            console.log('\n🔍 STEP 7: CHECKING FOR DUPLICATE NATIONAL ID...');
            console.log(`Checking for existing National ID: ${formData.National_ID}`);
            const duplicateCheck = await pool.request()
                .input('nationalId', sql.NVarChar, formData.National_ID)
                .query(`
                    SELECT CandidateId, Name
                    FROM [SHMS].[dbo].[Candidates]
                    WHERE National_ID = @nationalId
                `);

            console.log(`Found ${duplicateCheck.recordset.length} existing records with this National ID`);
            if (duplicateCheck.recordset.length > 0) {
                console.log(`❌ Duplicate found: ${duplicateCheck.recordset[0].Name} (ID: ${duplicateCheck.recordset[0].CandidateId})`);
                return {
                    success: false,
                    errors: ['A candidate with this National ID already exists'],
                    step: 'duplicate_check',
                    existingCandidate: duplicateCheck.recordset[0]
                };
            }
            console.log('✅ No duplicates found');
        }

        // Step 6: Insert candidate data (let database auto-generate CandidateId)
        console.log('\n💾 STEP 8: INSERTING CANDIDATE DATA...');
        const insertQuery = `
            INSERT INTO [SHMS].[dbo].[Candidates] (
                [CV_Path], [Name], [Main_Position], [SECTIONId], [UID], [Grade_ID], [Category_ID], [FID],
                [Sub_Position], [Expected_Salary], [National_ID], [Passport_Number], [Age], [Marital_Status],
                [Current_Address], [Mobile], [Email], [Military_Status],
                [CategoryName_AR], [GradeName_AR], [Grade], [Grad_Year], [Faculty],
                [uniname_ar], [Current_Employer], [Job_Title], [EXP_Years_From],
                [EXP_Years_To], [Current_Salary], [Leave_Reason], [Hear_About_Us],
                [EMP_ENG_LVL], [EMP_PC_LVL], [Has_Relative_In_Hospital],
                [Worked_At_Shifa_Before], [Has_Chronic_Disease], [Application_Status],
                [Created_At], [Updated_At], [Religion], [Gender], [HIMS_ID], [YearsOfExperience]
            )
            OUTPUT INSERTED.CandidateId
            VALUES (
                @CVPath, @Name, @MainPosition, @SECTIONId, @UID, @GradeID, @CategoryID, @FID,
                @SubPosition, @ExpectedSalary, @NationalID, @PassportNumber, @Age, @MaritalStatus,
                @CurrentAddress, @Mobile, @Email, @MilitaryStatus,
                @CategoryNameAR, @GradeNameAR, @Grade, @GradYear, @Faculty,
                @UniversityName, @CurrentEmployer, @JobTitle, @ExpYearsFrom,
                @ExpYearsTo, @CurrentSalary, @LeaveReason, @HearAboutUs,
                @EmpEngLvl, @EmpPcLvl, @HasRelative, @WorkedAtShifa,
                @HasChronicDisease, @ApplicationStatus, @CreatedAt, @UpdatedAt,
                @Religion, @Gender, @HIMSID, @YearsOfExperience
            )
        `;

        const insertResult = await pool.request()
            .input('CVPath', sql.NVarChar(500), mappedData.CV_Path)
            .input('Name', sql.NVarChar(255), mappedData.Name)
            .input('MainPosition', sql.Int, mappedData.Main_Position)
            .input('SECTIONId', sql.Int, mappedData.SECTIONId)
            .input('UID', sql.Int, mappedData.UID)
            .input('GradeID', sql.Int, mappedData.Grade_ID)
            .input('CategoryID', sql.Int, mappedData.Category_ID)
            .input('FID', sql.Int, mappedData.FID)
            .input('SubPosition', sql.NVarChar(255), mappedData.Sub_Position)
            .input('ExpectedSalary', sql.Decimal(18,2), mappedData.Expected_Salary)
            .input('NationalID', sql.NVarChar(50), mappedData.National_ID)
            .input('PassportNumber', sql.NVarChar(50), mappedData.Passport_Number)
            .input('Age', sql.Int, mappedData.Age)
            .input('MaritalStatus', sql.NVarChar(50), mappedData.Marital_Status)
            .input('CurrentAddress', sql.NVarChar(500), mappedData.Current_Address)
            .input('Mobile', sql.NVarChar(50), mappedData.Mobile)
            .input('Email', sql.NVarChar(255), mappedData.Email)
            .input('MilitaryStatus', sql.NVarChar(50), mappedData.Military_Status)
            .input('CategoryNameAR', sql.NVarChar(100), mappedData.CategoryName_AR)
            .input('GradeNameAR', sql.NVarChar(100), mappedData.GradeName_AR)
            .input('Grade', sql.NVarChar(50), mappedData.Grade)
            .input('GradYear', sql.NVarChar(50), mappedData.Grad_Year)
            .input('Faculty', sql.NVarChar(255), mappedData.Faculty)
            .input('UniversityName', sql.NVarChar(255), mappedData.uniname_ar)
            .input('CurrentEmployer', sql.NVarChar(255), mappedData.Current_Employer)
            .input('JobTitle', sql.NVarChar(255), mappedData.Job_Title)
            .input('ExpYearsFrom', sql.Date, mappedData.EXP_Years_From)
            .input('ExpYearsTo', sql.Date, mappedData.EXP_Years_To)
            .input('CurrentSalary', sql.Decimal(18,2), mappedData.Current_Salary)
            .input('LeaveReason', sql.NVarChar(500), mappedData.Leave_Reason)
            .input('HearAboutUs', sql.NVarChar(100), mappedData.Hear_About_Us)
            .input('EmpEngLvl', sql.NVarChar(50), mappedData.EMP_ENG_LVL)
            .input('EmpPcLvl', sql.NVarChar(50), mappedData.EMP_PC_LVL)
            .input('HasRelative', sql.NVarChar(10), mappedData.Has_Relative_In_Hospital)
            .input('WorkedAtShifa', sql.NVarChar(10), mappedData.Worked_At_Shifa_Before)
            .input('HasChronicDisease', sql.NVarChar(10), mappedData.Has_Chronic_Disease)
            .input('ApplicationStatus', sql.NVarChar(50), mappedData.Application_Status)
            .input('CreatedAt', sql.DateTime, new Date(mappedData.Created_At))
            .input('UpdatedAt', sql.DateTime, new Date(mappedData.Updated_At))
            .input('Religion', sql.NVarChar(50), mappedData.Religion)
            .input('Gender', sql.NVarChar(20), mappedData.Gender)
            .input('HIMSID', sql.Int, mappedData.HIMS_ID)
            .input('YearsOfExperience', sql.Int, mappedData.YearsOfExperience)
            .query(insertQuery);

        // Get the auto-generated CandidateId from the OUTPUT clause
        const newCandidateId = insertResult.recordset[0].CandidateId;
        console.log(`✅ STEP 9: Candidate successfully inserted with auto-generated ID: ${newCandidateId}`);

        return {
            success: true,
            candidateId: newCandidateId,
            message: 'Application submitted successfully',
            data: mappedData
        };

    } catch (error) {
        console.error('Error processing bilingual form submission:', error);
        return {
            success: false,
            errors: [error.message],
            step: 'processing_error',
            details: error.stack
        };
    } finally {
        if (pool) {
            try {
                await pool.close();
            } catch (closeError) {
                console.error('Error closing database connection:', closeError);
            }
        }
    }
}

// Routes
app.get('/', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);

        const { DepartmentName, PositionName, token } = req.query;

        // Check if there's a token parameter and fetch candidate data
        let candidateData = null;
        if (token) {
            try {
                // First check if the token columns exist
                const checkColumnsQuery = `
                    SELECT
                        CASE WHEN EXISTS (
                            SELECT 1
                            FROM INFORMATION_SCHEMA.COLUMNS
                            WHERE TABLE_SCHEMA = 'dbo'
                            AND TABLE_NAME = 'Candidates'
                            AND UPPER(COLUMN_NAME) = 'COMPLETIONTOKEN'
                        ) THEN 1 ELSE 0 END AS HasCompletionToken,
                        CASE WHEN EXISTS (
                            SELECT 1
                            FROM INFORMATION_SCHEMA.COLUMNS
                            WHERE TABLE_SCHEMA = 'dbo'
                            AND TABLE_NAME = 'Candidates'
                            AND UPPER(COLUMN_NAME) = 'TOKENEXPIRY'
                        ) THEN 1 ELSE 0 END AS HasTokenExpiry
                `;

                const columnsResult = await pool.request().query(checkColumnsQuery);
                console.log('Column check results:', JSON.stringify(columnsResult.recordset[0]));
                const hasTokenColumns = columnsResult.recordset[0].HasCompletionToken == 1
                                     && columnsResult.recordset[0].HasTokenExpiry == 1;
                console.log('Has token columns:', hasTokenColumns);

                // Only try to fetch by token if the columns exist
                if (hasTokenColumns) {
                    const tokenQuery = `
                        SELECT *
                        FROM [SHMS].[dbo].[Candidates]
                        WHERE CompletionToken = @token
                        AND TokenExpiry > GETDATE()
                    `;

                    const tokenResult = await pool.request()
                        .input('token', sql.NVarChar, token)
                        .query(tokenQuery);

                    if (tokenResult.recordset.length > 0) {
                        candidateData = tokenResult.recordset[0];
                    }
                } else {
                    console.log('Token columns not found in database. Add the following columns:');
                    console.log("ALTER TABLE [SHMS].[dbo].[Candidates] ADD CompletionToken NVARCHAR(255) NULL;");
                    console.log("ALTER TABLE [SHMS].[dbo].[Candidates] ADD TokenExpiry DATETIME NULL;");

                    // Fallback - try to find candidate by matching CandidateId with token
                    // This assumes the token might be a candidate ID
                    const fallbackQuery = `
                        SELECT *
                        FROM [SHMS].[dbo].[Candidates]
                        WHERE CandidateId = @candidateId
                        AND Application_Status = 'Draft'
                    `;

                    try {
                        // Try to parse the token as a number (candidateId)
                        const possibleId = parseInt(token);
                        if (!isNaN(possibleId)) {
                            const fallbackResult = await pool.request()
                                .input('candidateId', sql.Int, possibleId)
                                .query(fallbackQuery);

                            if (fallbackResult.recordset.length > 0) {
                                candidateData = fallbackResult.recordset[0];
                                console.log('Found candidate using fallback ID method');
                            }
                        }
                    } catch (err) {
                        console.log('Fallback token search failed:', err.message);
                    }
                }
            } catch (tokenErr) {
                console.error('Error in token-based fetch:', tokenErr);
                // Continue without token data
            }
        }

        // Get departments
        const departmentsQuery = `
            SELECT DISTINCT
                DepartmentId,
                DepartmentName
            FROM [SHMS].[dbo].[Departments]
            WHERE IsActive = 1
            ORDER BY DepartmentName
        `;

        const departments = await pool.request().query(departmentsQuery);

        // Get positions for the preselected department
        let positions = [];
        if (DepartmentName) {
            const positionsQuery = `
                SELECT DISTINCT
                    jp.PositionId,
                    jp.PositionName,
                    jp.DepartmentId
                FROM [SHMS].[dbo].[JobPositions] jp
                INNER JOIN [SHMS].[dbo].[Departments] d
                    ON jp.DepartmentId = d.DepartmentId
                WHERE d.DepartmentName = @DepartmentName
                    AND jp.IsActive = 1
                ORDER BY jp.PositionName
            `;

            const positionsResult = await pool.request()
                .input('DepartmentName', sql.NVarChar, DepartmentName)
                .query(positionsQuery);

            positions = positionsResult.recordset;
        } else if (candidateData && candidateData.Main_Position) {
            // If candidate data exists with a department, fetch related positions
            const positionsQuery = `
                SELECT DISTINCT
                    jp.PositionId,
                    jp.PositionName,
                    jp.DepartmentId
                FROM [SHMS].[dbo].[JobPositions] jp
                WHERE jp.DepartmentId = @DepartmentId
                    AND jp.IsActive = 1
                ORDER BY jp.PositionName
            `;

            const positionsResult = await pool.request()
                .input('DepartmentId', sql.Int, candidateData.Main_Position)
                .query(positionsQuery);

            positions = positionsResult.recordset;
        }

        // Log the data being sent to the template
        console.log('Rendering with data:', {
            departmentName: DepartmentName,
            positionName: PositionName,
            positionsFound: positions.length,
            positions: positions,
            hasToken: !!token,
            candidateData: candidateData ? candidateData : 'Not found'
        });

        res.render('index', {
            departments: departments.recordset,
            positions: positions,
            preselectedDepartment: candidateData ? candidateData.Main_Position : (DepartmentName || null),
            preselectedPosition: candidateData ? candidateData.Sub_Position : (PositionName || null),
            isPreselected: !!(DepartmentName || PositionName || candidateData),
            candidateData: candidateData,
            completionToken: token || null
        });
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).send('Server Error');
    } finally {
        if (pool) {
            try {
                await pool.close();
            } catch (err) {
                console.error('Error closing connection:', err);
            }
        }
    }
});

app.get('/api/positions/:departmentId', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);

        const departmentId = req.params.departmentId;

        const query = `
            SELECT DISTINCT
                PositionId,
                PositionName
            FROM [SHMS].[dbo].[JobPositions]
            WHERE DepartmentId = @departmentId
                AND IsActive = 1
            ORDER BY PositionName
        `;

        const result = await pool.request()
            .input('departmentId', sql.Int, departmentId)
            .query(query);

        res.json(result.recordset);

    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({
            error: 'Failed to fetch positions',
            details: err.message
        });
    } finally {
        if (pool) await pool.close();
    }
});

// Add new endpoint to get all positions with section and department info
app.get('/api/positions', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);

        const query = `
            SELECT
                JP.HIMS_ID,
                JP.PositionName,
                D.DepartmentId,
                D.DepartmentName,
                S.SECTIONId,
                S.SECTIONName
            FROM [SHMS].[dbo].[JobPositions] JP
            JOIN [SHMS].[dbo].[Departments] D ON d.DepartmentId = JP.DepartmentId
            JOIN [SHMS].[dbo].[SECTIONS] S ON S.SECTIONId = JP.SECTIONId
            WHERE JP.IsActive = 1
            ORDER BY JP.PositionName
        `;

        const result = await pool.request().query(query);
        res.json(result.recordset);

    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({
            error: 'Failed to fetch positions',
            details: err.message
        });
    } finally {
        if (pool) await pool.close();
    }
});

// Add endpoint to get position details by HIMS_ID
app.get('/api/position/:himsId', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);

        // Safely parse himsId as integer or null
        let himsId = null;
        try {
            himsId = parseInt(req.params.himsId);
            if (isNaN(himsId)) {
                throw new Error('Invalid HIMS_ID format');
            }
        } catch (parseError) {
            console.warn('Failed to parse himsId:', req.params.himsId, parseError);
            return res.status(400).json({
                error: 'Invalid HIMS_ID format',
                details: 'The HIMS_ID must be a valid integer.'
            });
        }

        // Get position details
        const query = `
            SELECT
                JP.PositionId,
                JP.HIMS_ID,
                JP.PositionName,
                JP.Description,
                D.DepartmentId,
                D.DepartmentName,
                S.SECTIONId,
                S.SECTIONName
            FROM [SHMS].[dbo].[JobPositions] JP
            LEFT JOIN [SHMS].[dbo].[Departments] D ON JP.DepartmentId = D.DepartmentId
            LEFT JOIN [SHMS].[dbo].[SECTIONS] S ON JP.SECTIONId = S.SECTIONId
            WHERE JP.HIMS_ID = @himsId
        `;

        const result = await pool.request()
            .input('himsId', sql.Int, himsId)
            .query(query);

        if (result.recordset.length === 0) {
            res.status(404).json({ error: 'Position not found' });
            return;
        }

        res.json(result.recordset[0]);
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({ error: 'Failed to fetch position details' });
    } finally {
        if (pool) await pool.close();
    }
});

// Modify the submit-application route to skip the SharePoint API call
app.post('/submit-application', async (req, res) => {
    console.log('Received application submission request');
    const isMobile = req.isMobile || req.body.isMobileSubmission;

    if (isMobile) {
        console.log('Mobile submission detected, applying special handling');
    }

    // Create a unique session ID for this submission
    const submissionId = Date.now() + '-' + Math.random().toString(36).substring(2, 10);
    console.log(`Starting submission ${submissionId}`);

    // Log the data that would have been sent to SharePoint
    console.log('SKIPPING SharePoint API submission. Data that would have been sent:');
    console.log(JSON.stringify(req.body, null, 2));

    // Track submission attempts for this session
    if (!req.session) {
        req.session = {};
    }

    if (!req.session.submissionAttempts) {
        req.session.submissionAttempts = 0;
    }
    req.session.submissionAttempts++;

    console.log(`Submission attempt #${req.session.submissionAttempts} for session`);

    try {
        // Return success without actually calling SharePoint API
        console.log(`Submission ${submissionId} completed successfully (SharePoint API call skipped)`);

        // Clear submission attempts on success
        if (req.session) {
            req.session.submissionAttempts = 0;
        }

        res.json({
            success: true,
            data: {
                message: "SharePoint API submission skipped",
                submissionId: submissionId,
                timestamp: new Date().toISOString()
            },
            submissionId
        });
    } catch (error) {
        console.error('Error in application submission:', {
            submissionId,
            message: error.message,
            stack: error.stack,
            fullError: error,
            isMobile: isMobile
        });

        res.status(500).json({
            success: false,
            error: 'Failed to submit application',
            message: error.message,
            details: error.message,
            errorType: error.name,
            submissionId: submissionId,
            isMobile: isMobile
        });
    }
});

// Update the duplicate notification route
app.post('/send-duplicate-notification', async (req, res) => {
    console.log('Received duplicate notification request');
    const { nationalId, applicantName } = req.body;

    // Construct SharePoint list URL with filter
    const sharePointLink = `http://sh-sp19-01/bu/SH/HR/REC/Lists/Job%20Application/AllItems.aspx?viewid=57d22877%2D78ac%2D42ee%2D9f6b%2D12a2efea6a48&useFiltersInViewXml=1&FilterField1=ID%5FNo&FilterValue1=${nationalId}&FilterType1=Text`;

    console.log('Email notification details:', {
        nationalId,
        applicantName,
        sharePointLink
    });

    // Email content
    const mailOptions = {
        from: process.env.EMAIL_USER,
        to: '<EMAIL>',
        cc: '<EMAIL>',
        subject: 'Duplicate ID Number Detected in Job Application',
        html: `
            <h3>Duplicate ID Number Detected</h3>
            <p>A job application was submitted with a duplicate ID Number.</p>
            <p><strong>Details:</strong></p>
            <ul>
                <li>Applicant Name: ${applicantName}</li>
                <li>National ID: ${nationalId}</li>
            </ul>
            <p><strong>SharePoint Record Link:</strong></p>
            <p><a href="${sharePointLink}">Click here to view the duplicate record</a></p>
        `,
        headers: {
            'priority': 'high'
        }
    };

    try {
        console.log('Attempting to send email with config:', {
            host: transporter.options.host,
            port: transporter.options.port,
            secure: transporter.options.secure,
            user: transporter.options.auth.user,
            from: mailOptions.from,
            to: mailOptions.to
        });

        const info = await transporter.sendMail(mailOptions);
        console.log('Email sent successfully. Response:', {
            messageId: info.messageId,
            response: info.response,
            envelope: info.envelope
        });

        res.json({
            success: true,
            message: 'Notification email sent successfully',
            messageId: info.messageId
        });
    } catch (error) {
        console.error('Detailed email error:', {
            name: error.name,
            message: error.message,
            code: error.code,
            command: error.command,
            response: error.response,
            stack: error.stack
        });

        res.status(500).json({
            success: false,
            message: `Failed to send notification email: ${error.message}`,
            error: {
                name: error.name,
                message: error.message,
                code: error.code,
                stack: error.stack
            }
        });
    }
});

// Add route for sending error emails
app.post('/send-error-email', async (req, res) => {
    const { error, formData } = req.body;
    const timestamp = new Date().toLocaleString('en-US', { timeZone: 'Africa/Cairo' });

    const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc3545;">Application Submission Error</h2>
            <p style="color: #666;">An error occurred during application submission at ${timestamp}</p>

            <h3 style="color: #333;">Error Details:</h3>
            <div style="background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <p style="color: #721c24; margin: 0;">${error}</p>
            </div>

            <h3 style="color: #333;">Applicant Information:</h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                <p><strong>Name:</strong> ${formData.Name}</p>
                <p><strong>ID:</strong> ${formData.ID}</p>
                <p><strong>Email:</strong> ${formData.Email}</p>
                <p><strong>Phone:</strong> ${formData.Mobile}</p>
                <p><strong>Department:</strong> ${formData.Main_position}</p>
                <p><strong>Position:</strong> ${formData.Sub_Position}</p>
            </div>

            <div style="margin-top: 20px; font-size: 0.9em; color: #666;">
                <p>This is an automated message from the Job Application System.</p>
            </div>
        </div>
    `;

    const mailOptions = {
        from: process.env.EMAIL_USER,
        to: process.env.ADMIN_EMAIL,
        subject: `Job Application Error - ${formData.Name}`,
        html: htmlContent
    };

    try {
        await transporter.sendMail(mailOptions);
        console.log('Error report email sent successfully');
        res.json({ success: true, message: 'Error report sent successfully' });
    } catch (emailError) {
        console.error('Error sending email:', emailError);
        res.status(500).json({
            success: false,
            message: 'Failed to send error report',
            error: emailError.message
        });
    }
});

// Modify the upload-cv route
app.post('/upload-cv', async (req, res) => {
    try {
        if (!req.files || !req.files.cvFile) {
            return res.status(400).json({ success: false, message: 'No file uploaded' });
        }

        const cvFile = req.files.cvFile;
        const departmentId = req.body.departmentId;
        const positionId = req.body.positionId;
        const candidateName = req.body.candidateName.trim();

        // Sanitize filename
        const sanitizePath = (str) => {
            return str.replace(/[<>:"/\\|?*\x00-\x1F]/g, '_')
                     .replace(/\s+/g, '_');
        };

        // Base directory for CV files
        const baseDir = '\\\\192.168.10.2\\LaboratoryPDF$\\ShifaPortal';

        // Use IDs directly (without 'dept_' or 'pos_' prefix)
        const departmentDir = path.join(baseDir, departmentId);
        try {
            if (!fs.existsSync(departmentDir)) {
                fs.mkdirSync(departmentDir, { recursive: true });
            }
        } catch (mkdirError) {
            console.error('Error creating department directory:', mkdirError);
            throw new Error(`Failed to create department directory: ${mkdirError.message}`);
        }

        const positionDir = path.join(departmentDir, positionId);
        try {
            if (!fs.existsSync(positionDir)) {
                fs.mkdirSync(positionDir, { recursive: true });
            }
        } catch (mkdirError) {
            console.error('Error creating position directory:', mkdirError);
            throw new Error(`Failed to create position directory: ${mkdirError.message}`);
        }

        // Generate unique filename
        const timestamp = new Date().getTime();
        const sanitizedName = sanitizePath(candidateName);
        const filename = `${sanitizedName}_${timestamp}${path.extname(cvFile.name)}`;
        const filePath = path.join(positionDir, filename);

        // Move the file to the destination
        try {
            await cvFile.mv(filePath);
        } catch (moveError) {
            console.error('Error moving file:', moveError);
            throw new Error(`Failed to save file: ${moveError.message}`);
        }

        // Return the full network path for database storage
        const fullPath = path.join(
            baseDir,
            departmentId,
            positionId,
            filename
        ).replace(/\\/g, '\\\\'); // Ensure proper escaping of backslashes for DB storage

        res.json({
            success: true,
            filePath: fullPath
        });

    } catch (error) {
        console.error('Error uploading CV:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to upload CV file',
            error: error.message
        });
    }
});

// Modify the save-candidate endpoint to prevent duplicate records
app.post('/api/save-candidate', async (req, res) => {
    let pool;
    let transaction;
    try {
        pool = await sql.connect(config);

        // Start a transaction to prevent race conditions
        transaction = new sql.Transaction(pool);
        await transaction.begin();

        const formData = req.body;
        const currentDate = new Date().toISOString();

        // Get token from either the request body or the query parameters
        // This handles both form submissions and direct URL access with ?token=
        const token = formData.completionToken || req.query.token;
        let isTokenUpdate = !!token;  // Set to true if token exists

        // Check if this is an event submission
        const isEvent = formData.isEvent === 'true';
        const submissionSource = formData.submissionSource || null;

        // Define application status variable at the beginning of the function
        let applicationStatus = 'New';  // Default value

        // IMPORTANT: Force the Application_Status to 'Event' for event form submissions
        if (isEvent ||
            req.headers['x-event-form'] === 'true' ||
            formData.submissionSource ||
            (formData.Application_Status && formData.Application_Status.toLowerCase() === 'event')) {

            console.log('EVENT SUBMISSION DETECTED - Setting status to Event');
            formData.Application_Status = 'Event';
            applicationStatus = 'Event';  // Set our variable too
        }

        console.log('Received save-candidate request with data:', {
            nationalId: formData.National_ID,
            isTokenUpdate: isTokenUpdate,
            tokenValue: token || 'None',
            isEvent: isEvent,
            submissionSource: submissionSource,
            applicationStatus: formData.Application_Status
        });

        // Check if token columns exist
        const checkColumnsQuery = `
            SELECT
                CASE WHEN EXISTS (
                    SELECT 1
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = 'dbo'
                    AND TABLE_NAME = 'Candidates'
                    AND UPPER(COLUMN_NAME) = 'COMPLETIONTOKEN'
                ) THEN 1 ELSE 0 END AS HasCompletionToken,
                CASE WHEN EXISTS (
                    SELECT 1
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = 'dbo'
                    AND TABLE_NAME = 'Candidates'
                    AND UPPER(COLUMN_NAME) = 'TOKENEXPIRY'
                ) THEN 1 ELSE 0 END AS HasTokenExpiry
        `;

        const columnsResult = await pool.request().query(checkColumnsQuery);
        console.log('Column check results:', JSON.stringify(columnsResult.recordset[0]));
        const hasTokenColumns = columnsResult.recordset[0].HasCompletionToken == 1
                             && columnsResult.recordset[0].HasTokenExpiry == 1;
        console.log('Has token columns:', hasTokenColumns);

        // Check for existing record using National ID or token
        let checkQuery, checkRequest = new sql.Request(transaction);

            if (token) {
                // If we have a token, use it to find the existing record
                isTokenUpdate = true;
                checkQuery = `
                    SELECT CandidateId FROM Candidates
                    WHERE CompletionToken = @Token
                `;
                checkRequest.input('Token', sql.NVarChar(255), token);
                console.log('Checking for existing record using token:', token);
                console.log('isTokenUpdate flag is now:', isTokenUpdate);
            } else if (formData.National_ID) {
                // If no token, check if record exists with the same National ID
                // But skip this check for passport type IDs
                if (formData.ID_Type === 'passport') {
                    console.log('Passport type ID detected, skipping duplicate National ID check');
                    checkQuery = `SELECT CandidateId FROM Candidates WHERE 1=0`; // Will return empty result
                } else {
                    checkQuery = `
                        SELECT CandidateId FROM Candidates
                        WHERE National_ID = @NationalID
                    `;
                }
                checkRequest.input('NationalID', sql.NVarChar(50), formData.National_ID);
                console.log('Checking for existing record using National ID:', formData.National_ID);
            } else {
                throw new Error('No National ID or token provided');
            }

        const checkResult = await checkRequest.query(checkQuery);
        const recordExists = checkResult.recordset.length > 0;
        console.log('Check result:', recordExists ? `Record exists with ID: ${checkResult.recordset[0].CandidateId}` : 'Record does not exist');

        // If token update but no record found with that token, return appropriate error
        if (isTokenUpdate && !recordExists) {
            console.error('Token provided but no matching record found:', token);
            return res.status(404).json({
                success: false,
                error: 'The provided token is invalid or has expired',
                details: 'No matching record was found for this completion token'
            });
        }

        let query;
        let request = new sql.Request(transaction);

        if (recordExists) {
            // Update existing record - base query without token fields
            const candidateId = checkResult.recordset[0].CandidateId;
            console.log(`Updating existing record with ID: ${candidateId}`);

            let baseQuery = `
                UPDATE [SHMS].[dbo].[Candidates]
                SET
                    [CV_Path] = COALESCE(@CVPath, [CV_Path]),
                    [Name] = @Name,
                    [Main_Position] = @MainPosition,
                    [SECTIONId] = @SECTIONId,
                    [UID] = @UID,
                    [Grade_ID] = @GradeID,
                    [Category_ID] = @CategoryID,
                    [FID] = @FID,
                    [Sub_Position] = @SubPosition,
                    [Expected_Salary] = @ExpectedSalary,
                    [National_ID] = @NationalID,
                    [Passport_Number] = @PassportNumber,
                    [Age] = @Age,
                    [Marital_Status] = @MaritalStatus,
                    [Current_Address] = @CurrentAddress,
                    [Mobile] = @Mobile,
                    [Email] = @Email,
                    [Military_Status] = @MilitaryStatus,
                    [CategoryName_AR] = @CategoryNameAR,
                    [GradeName_AR] = @GradeNameAR,
                    [Grade] = @Grade,
                    [Grad_Year] = @GradYear,
                    [Faculty] = @Faculty,
                    [uniname_ar] = @UniversityName,
                    [Current_Employer] = @CurrentEmployer,
                    [Job_Title] = @JobTitle,
                    [EXP_Years_From] = @ExpYearsFrom,
                    [EXP_Years_To] = @ExpYearsTo,
                    [Current_Salary] = @CurrentSalary,
                    [Leave_Reason] = @LeaveReason,
                    [Hear_About_Us] = @HearAboutUs,
                    [EMP_ENG_LVL] = @EmpEngLvl,
                    [EMP_PC_LVL] = @EmpPcLvl,
                    [Has_Relative_In_Hospital] = @HasRelative,
                    [Worked_At_Shifa_Before] = @WorkedAtShifa,
                    [Has_Chronic_Disease] = @HasChronicDisease,
                    [Updated_At] = @UpdatedAt,
                    [Application_Status] = CASE WHEN @IsTokenUpdate = 1 THEN 'Updated' ELSE 'Updated' END,
                    [Religion] = @Religion,
                    [Gender] = @Gender,
                    [HIMS_ID] = @HIMSID,
                    [Source] = CASE WHEN @IsEvent = 1 THEN @SubmissionSource ELSE [Source] END
            `;

            // Add token fields if they exist
            if (hasTokenColumns) {
                baseQuery += `,
                    [CompletionToken] = CASE WHEN @IsTokenUpdate = 1 THEN NULL ELSE [CompletionToken] END,
                    [TokenExpiry] = CASE WHEN @IsTokenUpdate = 1 THEN NULL ELSE [TokenExpiry] END
                `;
            }

            // Finish the query with the appropriate WHERE clause
            if (isTokenUpdate && hasTokenColumns) {
                baseQuery += `
                    WHERE CompletionToken = @Token
                `;
                request.input('Token', sql.NVarChar(255), token);
            } else {
                baseQuery += `
                    WHERE CandidateId = @CandidateId
                `;
                request.input('CandidateId', sql.Int, candidateId);
            }

            query = baseQuery;
        } else {
            // Insert new record - base query without token fields
            console.log('Creating new record');

            // For event submissions, ensure status is Event (redundant check for safety)
            if (isEvent ||
                req.headers['x-event-form'] === 'true' ||
                formData.submissionSource ||
                (formData.Application_Status && formData.Application_Status.toLowerCase() === 'event')) {
                applicationStatus = 'Event';
                console.log('Setting Application_Status to Event for event submission');
            }

            let insertFields = `
                    [CV_Path], [Name], [Main_Position], [SECTIONId], [UID], [Grade_ID], [Category_ID], [FID],
                    [Sub_Position], [Expected_Salary], [National_ID], [Passport_Number], [Age], [Marital_Status],
                    [Current_Address], [Mobile], [Email], [Military_Status],
                    [CategoryName_AR], [GradeName_AR], [Grade], [Grad_Year], [Faculty],
                    [uniname_ar], [Current_Employer], [Job_Title], [EXP_Years_From],
                    [EXP_Years_To], [Current_Salary], [Leave_Reason], [Hear_About_Us],
                    [EMP_ENG_LVL], [EMP_PC_LVL], [Has_Relative_In_Hospital],
                    [Worked_At_Shifa_Before], [Has_Chronic_Disease], [Application_Status],
                    [Created_At], [Updated_At], [Religion], [Gender], [HIMS_ID], [Source]
            `;

            let valueFields = `
                    @CVPath, @Name, @MainPosition, @SECTIONId, @UID, @GradeID, @CategoryID, @FID,
                    @SubPosition, @ExpectedSalary, @NationalID, @PassportNumber, @Age, @MaritalStatus,
                    @CurrentAddress, @Mobile, @Email, @MilitaryStatus,
                    @CategoryNameAR, @GradeNameAR, @Grade, @GradYear, @Faculty,
                    @UniversityName, @CurrentEmployer, @JobTitle, @ExpYearsFrom,
                    @ExpYearsTo, @CurrentSalary, @LeaveReason, @HearAboutUs,
                    @EmpEngLvl, @EmpPcLvl, @HasRelative, @WorkedAtShifa,
                    @HasChronicDisease, @ApplicationStatus, @CreatedAt, @UpdatedAt,
                    @Religion, @Gender, @HIMSID, @SubmissionSource
            `;

            // Add token fields if they exist
            if (hasTokenColumns) {
                insertFields += `, [CompletionToken], [TokenExpiry]`;
                valueFields += `, NULL, NULL`;
            }

            // Build the final query
            query = `
                INSERT INTO [SHMS].[dbo].[Candidates] (
                    ${insertFields}
                )
                VALUES (
                    ${valueFields}
                );

                SELECT SCOPE_IDENTITY() AS NewCandidateId;
            `;
        }

        // Bind all parameters
        request
            .input('CVPath', sql.NVarChar(500), formData.CV_Path || null)
            .input('Name', sql.NVarChar(255), formData.Name)
            .input('MainPosition', sql.Int, formData.Main_Position)
            .input('SECTIONId', sql.Int, formData.SECTIONId)
            .input('UID', sql.Int, formData.UID)
            .input('GradeID', sql.Int, formData.Grade_ID)
            .input('CategoryID', sql.Int, formData.Category_ID)
            .input('FID', sql.Int, formData.FID)
            .input('SubPosition', sql.NVarChar(255), formData.Sub_Position)
            .input('ExpectedSalary', sql.Decimal(18,2), parseFloat(formData.Expected_Salary) || 0)
            .input('NationalID', sql.NVarChar(50), formData.National_ID)
            .input('PassportNumber', sql.NVarChar(50), formData.Passport_Number)
            .input('Age', sql.Int, formData.Age)
            .input('MaritalStatus', sql.NVarChar(50), formData.Marital_Status)
            .input('CurrentAddress', sql.NVarChar(500), formData.Current_Address)
            .input('Mobile', sql.NVarChar(50), formData.Mobile)
            .input('Email', sql.NVarChar(255), formData.Email)
            .input('MilitaryStatus', sql.NVarChar(50), formData.Military_Status)
            .input('CategoryNameAR', sql.NVarChar(100), formData.CategoryName_AR)
            .input('GradeNameAR', sql.NVarChar(100), formData.GradeName_AR)
            .input('Grade', sql.NVarChar(50), formData.Grade)
            .input('GradYear', sql.NVarChar(50), formData.Grad_Year)
            .input('Faculty', sql.NVarChar(255), formData.Faculty)
            .input('UniversityName', sql.NVarChar(255), formData.uniname_ar)
            .input('CurrentEmployer', sql.NVarChar(255), formData.Current_Employer)
            .input('JobTitle', sql.NVarChar(255), formData.Job_Title)
            .input('ExpYearsFrom', sql.Date, formData.EXP_Years_From ? new Date(formData.EXP_Years_From) : null)
            .input('ExpYearsTo', sql.Date, formData.EXP_Years_To ? new Date(formData.EXP_Years_To) : null)
            .input('CurrentSalary', sql.Decimal(18,2), parseFloat(formData.Current_Salary) || 0)
            .input('LeaveReason', sql.NVarChar(500), formData.Leave_Reason)
            .input('HearAboutUs', sql.NVarChar(100), formData.Hear_About_Us)
            .input('EmpEngLvl', sql.NVarChar(50), formData.EMP_ENG_LVL)
            .input('EmpPcLvl', sql.NVarChar(50), formData.EMP_PC_LVL)
            .input('HasRelative', sql.NVarChar(10), formData.Has_Relative_In_Hospital)
            .input('WorkedAtShifa', sql.NVarChar(10), formData.Worked_At_Shifa_Before)
            .input('HasChronicDisease', sql.NVarChar(10), formData.Has_Chronic_Disease)
            .input('CreatedAt', sql.DateTime, new Date(currentDate))
            .input('UpdatedAt', sql.DateTime, new Date(currentDate))
            .input('Religion', sql.NVarChar(50), formData.Religion)
            .input('Gender', sql.NVarChar(20), formData.Gender)
            .input('HIMSID', sql.Int, formData.HIMS_ID !== undefined && formData.HIMS_ID !== null && !isNaN(parseInt(formData.HIMS_ID)) ? parseInt(formData.HIMS_ID) : null)
            .input('IsTokenUpdate', sql.Bit, isTokenUpdate ? 1 : 0)
            .input('IsEvent', sql.Bit, isEvent ? 1 : 0)
            .input('SubmissionSource', sql.NVarChar(100), submissionSource)
            .input('ApplicationStatus', sql.NVarChar(50), applicationStatus || formData.Application_Status || 'New');

        // Execute the query
        const result = await request.query(query);

        // Get the ID of the inserted/updated record
        let candidateId;
        if (!recordExists && result.recordset && result.recordset[0]) {
            candidateId = result.recordset[0].NewCandidateId;
        } else if (recordExists) {
            candidateId = checkResult.recordset[0].CandidateId;
        }

        // Commit the transaction
        await transaction.commit();

        console.log(`Candidate saved to database successfully with ID: ${candidateId}`);
        res.json({
            success: true,
            message: `Candidate data ${recordExists ? 'updated' : 'saved'} successfully`,
            candidateId: candidateId,
            isUpdate: recordExists,
            tokenCleared: isTokenUpdate
        });

    } catch (error) {
        // Rollback the transaction in case of error
        if (transaction) {
            try {
                await transaction.rollback();
                console.log('Transaction rolled back due to error');
            } catch (rollbackError) {
                console.error('Error rolling back transaction:', rollbackError);
            }
        }

        console.error('Error saving candidate data:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to save candidate data',
            details: error.message
        });
    } finally {
        if (pool) await pool.close();
    }
});

// Get Qualifications
app.get('/api/qualifications', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT
                [Grade_ID],
                [GradeName_AR],
                [GradeName_EN],
                [Category_ID],
                [CategoryName_AR],
                [CategoryName_EN]
            FROM [SHMS].[dbo].[Qualifications]
        `);
        res.json(result.recordset);
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({ error: 'Failed to fetch qualifications' });
    } finally {
        if (pool) await pool.close();
    }
});

// Get Universities
app.get('/api/universities', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT
                [uni_id],
                [uniname_ar],
                [uniname_en]
            FROM [SHMS].[dbo].[University]
        `);
        res.json(result.recordset);
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({ error: 'Failed to fetch universities' });
    } finally {
        if (pool) await pool.close();
    }
});

// Get Scientific Degrees
app.get('/api/scientific-degrees', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT DISTINCT
                [Category_ID],
                [CategoryName_AR],
                [CategoryName_EN]
            FROM [SHMS].[dbo].[Qualifications]
            WHERE [Category_ID] IS NOT NULL AND [CategoryName_EN] IS NOT NULL
            ORDER BY [CategoryName_EN]
        `);
        res.json(result.recordset);
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({ error: 'Failed to fetch scientific degrees' });
    } finally {
        if (pool) await pool.close();
    }
});

// Get Specializations by Category ID
app.get('/api/specializations/:categoryId', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const categoryId = parseInt(req.params.categoryId);

        if (isNaN(categoryId)) {
            return res.status(400).json({ error: 'Invalid category ID' });
        }

        const result = await pool.request()
            .input('categoryId', sql.Int, categoryId)
            .query(`
                SELECT
                    [ID],
                    [Grade_ID],
                    [GradeName_AR],
                    [GradeName_EN],
                    [Category_ID],
                    [CategoryName_AR],
                    [CategoryName_EN]
                FROM [SHMS].[dbo].[Qualifications]
                WHERE [Category_ID] = @categoryId
                    AND [Grade_ID] IS NOT NULL
                    AND [GradeName_EN] IS NOT NULL
                ORDER BY [GradeName_EN]
            `);
        res.json(result.recordset);
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({ error: 'Failed to fetch specializations' });
    } finally {
        if (pool) await pool.close();
    }
});

// Get Universities (Updated)
app.get('/api/universities', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT
                [id],
                [uni_id],
                [uniname_ar],
                [uniname_en]
            FROM [SHMS].[dbo].[University]
            WHERE [uni_id] > 0 AND [uniname_en] IS NOT NULL
            ORDER BY [uniname_en]
        `);
        res.json(result.recordset);
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({ error: 'Failed to fetch universities' });
    } finally {
        if (pool) await pool.close();
    }
});

// Get Faculties (Updated)
app.get('/api/faculties', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT
                [id],
                [fac_id],
                [facname_ar],
                [facname_en]
            FROM [SHMS].[dbo].[Faculty]
            WHERE [facname_en] IS NOT NULL
            ORDER BY [facname_en]
        `);
        res.json(result.recordset);
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({ error: 'Failed to fetch faculties' });
    } finally {
        if (pool) await pool.close();
    }
});

// Get Jobs with pagination
app.get('/api/jobs', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);

        // Get pagination parameters
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        // Count total job postings
        const countQuery = `
            SELECT COUNT(*) AS total
            FROM [SHMS].[dbo].[JobPostings]
            WHERE IsActive = 1
        `;

        const countResult = await pool.request().query(countQuery);
        const total = countResult.recordset[0].total;

        // Get job postings with pagination
        const jobsQuery = `
            SELECT
                JP.PostingId,
                JP.Title,
                JP.Description,
                JP.NumberOfVacancies,
                JP.ExperienceRequired,
                JP.SalaryRange,
                JP.PublishDate,
                JP.ExpiryDate,
                JP.IMGURL,
                D.DepartmentName,
                P.PositionName
            FROM [SHMS].[dbo].[JobPostings] JP
            LEFT JOIN [SHMS].[dbo].[JobPositions] P ON JP.PositionId = P.PositionId
            LEFT JOIN [SHMS].[dbo].[Departments] D ON P.DepartmentId = D.DepartmentId
            WHERE JP.IsActive = 1
            ORDER BY JP.PublishDate DESC
            OFFSET ${offset} ROWS
            FETCH NEXT ${limit} ROWS ONLY
        `;

        const jobsResult = await pool.request().query(jobsQuery);

        // Calculate total pages
        const totalPages = Math.ceil(total / limit);

        res.json({
            success: true,
            data: jobsResult.recordset,
            pagination: {
                total,
                page,
                limit,
                totalPages
            }
        });

    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch job postings',
            details: err.message
        });
    } finally {
        if (pool) await pool.close();
    }
});

// Add a new endpoint to fetch candidate by token
app.get('/api/candidate-by-token/:token', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);

        const token = req.params.token;
        console.log('Searching for candidate with token:', token);

        // First check if the token columns exist
        const checkColumnsQuery = `
            SELECT
                CASE WHEN EXISTS (
                    SELECT 1
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = 'dbo'
                    AND TABLE_NAME = 'Candidates'
                    AND UPPER(COLUMN_NAME) = 'COMPLETIONTOKEN'
                ) THEN 1 ELSE 0 END AS HasCompletionToken,
                CASE WHEN EXISTS (
                    SELECT 1
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = 'dbo'
                    AND TABLE_NAME = 'Candidates'
                    AND UPPER(COLUMN_NAME) = 'TOKENEXPIRY'
                ) THEN 1 ELSE 0 END AS HasTokenExpiry
        `;

        const columnsResult = await pool.request().query(checkColumnsQuery);
        console.log('Column check results:', JSON.stringify(columnsResult.recordset[0]));
        const hasTokenColumns = columnsResult.recordset[0].HasCompletionToken == 1
                              && columnsResult.recordset[0].HasTokenExpiry == 1;
        console.log('Has token columns:', hasTokenColumns);

        let candidateData = null;

        // Only try to fetch by token if the columns exist
        if (hasTokenColumns) {
            const query = `
                SELECT *
                FROM [SHMS].[dbo].[Candidates]
                WHERE CompletionToken = @token
                AND (TokenExpiry IS NULL OR TokenExpiry > GETDATE())
            `;

            console.log('Running query with token:', token);
            const result = await pool.request()
                .input('token', sql.NVarChar, token)
                .query(query);

            console.log('Query results count:', result.recordset.length);
            if (result.recordset.length > 0) {
                candidateData = result.recordset[0];
            }
        } else {
            // Fallback - try to find candidate by matching CandidateId with token
            // This assumes the token might be a candidate ID
            const fallbackQuery = `
                SELECT *
                FROM [SHMS].[dbo].[Candidates]
                WHERE CandidateId = @candidateId
                AND Application_Status = 'Draft'
            `;

            try {
                // Try to parse the token as a number (candidateId)
                const possibleId = parseInt(token);
                if (!isNaN(possibleId)) {
                    const fallbackResult = await pool.request()
                        .input('candidateId', sql.Int, possibleId)
                        .query(fallbackQuery);

                    if (fallbackResult.recordset.length > 0) {
                        candidateData = fallbackResult.recordset[0];
                        console.log('API: Found candidate using fallback ID method');
                    }
                }
            } catch (err) {
                console.log('API: Fallback token search failed:', err.message);
            }
        }

        if (!candidateData) {
            res.status(404).json({
                success: false,
                error: 'Candidate not found or token expired'
            });
            return;
        }

        res.json({
            success: true,
            data: candidateData
        });

    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch candidate data',
            details: err.message
        });
    } finally {
        if (pool) await pool.close();
    }
});

// Phone Call Routes
app.get('/api/phone-calls', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT
                [CallId],
                [CandidateId],
                [CallerName],
                [CallDateTime],
                [CallDuration],
                [CallNotes],
                [CallStatus]
            FROM [SHMS].[dbo].[PhoneCalls]
            ORDER BY [CallDateTime] DESC
        `);
        res.json({
            success: true,
            data: result.recordset
        });
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch phone calls',
            details: err.message
        });
    } finally {
        if (pool) await pool.close();
    }
});

app.get('/api/phone-calls/:candidateId', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const candidateId = req.params.candidateId;

        const result = await pool.request()
            .input('candidateId', sql.Int, candidateId)
            .query(`
                SELECT
                    [CallId],
                    [CandidateId],
                    [CallerName],
                    [CallDateTime],
                    [CallDuration],
                    [CallNotes],
                    [CallStatus]
                FROM [SHMS].[dbo].[PhoneCalls]
                WHERE [CandidateId] = @candidateId
                ORDER BY [CallDateTime] DESC
            `);

        res.json({
            success: true,
            data: result.recordset
        });
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch phone calls for candidate',
            details: err.message
        });
    } finally {
        if (pool) await pool.close();
    }
});

app.post('/api/phone-calls', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const { candidateId, callerName, callDateTime, callDuration, callNotes, callStatus, outcome, applicationStatus } = req.body;

        console.log('===== POST NEW PHONE CALL DEBUGGING =====');

        // Validate required fields
        if (!candidateId || !callDateTime) {
            console.log('Error: Missing required fields');
            return res.status(400).json({
                success: false,
                error: 'Missing required fields'
            });
        }

        console.log('New phone call data received:', {
            candidateId,
            outcome,
            callStatus,
            applicationStatus
        });

        // Determine the final status value
        const finalStatus = callStatus || outcome || 'Completed';
        console.log('Final call status determined as:', finalStatus);

        // Create new phone call record
        const result = await pool.request()
            .input('candidateId', sql.Int, candidateId)
            .input('callerName', sql.NVarChar, callerName || '')
            .input('callDateTime', sql.DateTime, new Date(callDateTime))
            .input('callDuration', sql.Int, callDuration || 0)
            .input('callNotes', sql.NVarChar, callNotes || '')
            .input('callStatus', sql.NVarChar, finalStatus)
            .query(`
                INSERT INTO [SHMS].[dbo].[PhoneCalls]
                    ([CandidateId], [CallerName], [CallDateTime], [CallDuration], [CallNotes], [CallStatus])
                VALUES
                    (@candidateId, @callerName, @callDateTime, @callDuration, @callNotes, @callStatus);

                SELECT SCOPE_IDENTITY() AS CallId;
            `);

        const callId = result.recordset[0].CallId;
        console.log(`New phone call created with ID: ${callId}, status: ${finalStatus}`);

        // If applicationStatus is explicitly provided or outcome/status is "Not Interested"
        if (applicationStatus === 'Not Interested' || outcome === 'Not Interested' || callStatus === 'Not Interested') {
            console.log(`CRITICAL: Forcing update of candidate ${candidateId} status to 'Not Interested' via DIRECT SQL STATEMENT`);

            try {
                // DIRECT SQL STATEMENT APPROACH
                const directSqlResult = await pool.request()
                    .input('candidateId', sql.Int, candidateId)
                    .query(`
                        UPDATE [SHMS].[dbo].[Candidates]
                        SET [Application_Status] = 'Not Interested'
                        WHERE [CandidateId] = @candidateId;

                        SELECT @@ROWCOUNT AS RowsAffected;
                    `);

                console.log('DIRECT SQL RESULT:', directSqlResult.recordset[0]);

                if (directSqlResult.recordset[0].RowsAffected > 0) {
                    console.log('SUCCESS: Candidate status updated directly via SQL');
                } else {
                    console.log('WARNING: Direct SQL update affected 0 rows');
                }
            } catch (sqlError) {
                console.error('ERROR in direct SQL update:', sqlError);
            }

            // Verify the update was successful
            const verifyResult = await pool.request()
                .input('candidateId', sql.Int, candidateId)
                .query(`
                    SELECT [CandidateId], [Name], [Application_Status]
                    FROM [SHMS].[dbo].[Candidates]
                    WHERE [CandidateId] = @candidateId
                `);

            console.log('VERIFICATION - Updated candidate data:', verifyResult.recordset[0]);
        }

        console.log('===== END NEW PHONE CALL DEBUGGING =====');

        res.json({
            success: true,
            message: 'Phone call created successfully',
            callId: callId
        });
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({
            success: false,
            error: 'Failed to create phone call',
            details: err.message
        });
    } finally {
        if (pool) await pool.close();
    }
});

app.put('/api/phone-calls/:callId', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const callId = req.params.callId;
        const { callerName, callDateTime, callDuration, callNotes, callStatus } = req.body;

        // Build the update query dynamically based on provided fields
        let updateFields = [];
        let parameters = [];

        if (callerName !== undefined) {
            updateFields.push('[CallerName] = @callerName');
            parameters.push({ name: 'callerName', type: sql.NVarChar, value: callerName });
        }

        if (callDateTime !== undefined) {
            updateFields.push('[CallDateTime] = @callDateTime');
            parameters.push({ name: 'callDateTime', type: sql.DateTime, value: new Date(callDateTime) });
        }

        if (callDuration !== undefined) {
            updateFields.push('[CallDuration] = @callDuration');
            parameters.push({ name: 'callDuration', type: sql.Int, value: callDuration });
        }

        if (callNotes !== undefined) {
            updateFields.push('[CallNotes] = @callNotes');
            parameters.push({ name: 'callNotes', type: sql.NVarChar, value: callNotes });
        }

        if (callStatus !== undefined) {
            updateFields.push('[CallStatus] = @callStatus');
            parameters.push({ name: 'callStatus', type: sql.NVarChar, value: callStatus });
        }

        if (updateFields.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'No fields provided for update'
            });
        }

        const updateQuery = `
            UPDATE [SHMS].[dbo].[PhoneCalls]
            SET ${updateFields.join(', ')}
            WHERE [CallId] = @callId;

            SELECT @@ROWCOUNT AS RowsAffected;
        `;

        const request = pool.request().input('callId', sql.Int, callId);

        // Add all parameters to the request
        parameters.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const result = await request.query(updateQuery);

        if (result.recordset[0].RowsAffected === 0) {
            return res.status(404).json({
                success: false,
                error: 'Phone call record not found'
            });
        }

        res.json({
            success: true,
            message: 'Phone call record updated successfully'
        });
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({
            success: false,
            error: 'Failed to update phone call record',
            details: err.message
        });
    } finally {
        if (pool) await pool.close();
    }
});

app.delete('/api/phone-calls/:callId', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const callId = req.params.callId;

        const result = await pool.request()
            .input('callId', sql.Int, callId)
            .query(`
                DELETE FROM [SHMS].[dbo].[PhoneCalls]
                WHERE [CallId] = @callId;

                SELECT @@ROWCOUNT AS RowsAffected;
            `);

        if (result.recordset[0].RowsAffected === 0) {
            return res.status(404).json({
                success: false,
                error: 'Phone call record not found'
            });
        }

        res.json({
            success: true,
            message: 'Phone call record deleted successfully'
        });
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).json({
            success: false,
            error: 'Failed to delete phone call record',
            details: err.message
        });
    } finally {
        if (pool) await pool.close();
    }
});

// Add route for event application form
app.get('/event-form', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);

        // Get event name from query parameter or use default
        const eventName = req.query.event || 'General Event';

        // Get departments for positions
        const departmentsQuery = `
            SELECT DISTINCT
                DepartmentId,
                DepartmentName
            FROM [SHMS].[dbo].[Departments]
            WHERE IsActive = 1
            ORDER BY DepartmentName
        `;

        const departments = await pool.request().query(departmentsQuery);

        res.render('event-form', {
            eventName: eventName,
            departments: departments.recordset
        });
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).send('Server Error');
    } finally {
        if (pool) {
            try {
                await pool.close();
            } catch (err) {
                console.error('Error closing connection:', err);
            }
        }
    }
});

// Add endpoint for bilingual form submission with comprehensive validation and mapping
app.post('/api/submit-bilingual-application', async (req, res) => {
    console.log('='.repeat(80));
    console.log('BILINGUAL FORM SUBMISSION DEBUG - START');
    console.log('='.repeat(80));

    // Step 1: Log the raw form data received from frontend
    console.log('\n📥 STEP 1: RAW FORM DATA RECEIVED FROM FRONTEND:');
    console.log('Form data keys:', Object.keys(req.body));
    console.log('Form data values:');
    Object.entries(req.body).forEach(([key, value]) => {
        console.log(`  ${key}: "${value}" (type: ${typeof value})`);
    });

    try {
        // Process the form submission using our comprehensive validation and mapping functions
        console.log('\n🔄 STEP 2: STARTING FORM PROCESSING...');
        const result = await processBilingualFormSubmission(req.body);

        if (result.success) {
            console.log(`Bilingual application submitted successfully with candidate ID: ${result.candidateId}`);
            res.json({
                success: true,
                message: result.message,
                candidateId: result.candidateId,
                data: {
                    candidateId: result.candidateId,
                    name: result.data.Name,
                    nationalId: result.data.National_ID,
                    passportNumber: result.data.Passport_Number,
                    position: result.data.Sub_Position,
                    department: result.data.Main_Position
                }
            });
        } else {
            console.log('Bilingual application submission failed:', result.errors);

            // Handle different types of errors
            if (result.step === 'duplicate_check') {
                res.status(409).json({
                    success: false,
                    error: 'Duplicate candidate',
                    message: 'A candidate with this National ID already exists',
                    errors: result.errors,
                    existingCandidate: result.existingCandidate
                });
            } else if (result.step === 'validation') {
                res.status(400).json({
                    success: false,
                    error: 'Validation failed',
                    message: 'Please correct the following errors',
                    errors: result.errors
                });
            } else {
                res.status(500).json({
                    success: false,
                    error: 'Processing error',
                    message: 'An error occurred while processing your application',
                    errors: result.errors,
                    details: result.details
                });
            }
        }

    } catch (error) {
        console.error('Unexpected error in bilingual form submission:', error);
        res.status(500).json({
            success: false,
            error: 'Server error',
            message: 'An unexpected error occurred',
            details: error.message
        });
    }
});

// ===== TEST API ENDPOINTS FOR DEBUGGING =====

// Test page route - Interactive HTML interface
app.get('/test-api', (req, res) => {
    const testPageHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bilingual Form Submission Test API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { color: #333; margin-top: 0; }
        .form-group { margin: 10px 0; }
        .form-group label { display: inline-block; width: 200px; font-weight: bold; }
        .form-group input, .form-group select { width: 300px; padding: 5px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .result-box { margin: 10px 0; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
        .result-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result-error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .result-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .sample-data { background: #e9ecef; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Bilingual Form Submission Test API</h1>
        <p>This page allows you to test each step of the form submission process individually to identify data mapping issues.</p>

        <!-- Sample Data Section -->
        <div class="test-section">
            <h3>📋 Sample Test Data</h3>
            <button class="btn btn-info" onclick="loadSampleData('valid')">Load Valid Sample Data</button>
            <button class="btn btn-warning" onclick="loadSampleData('invalid')">Load Invalid Sample Data</button>
            <button class="btn btn-danger" onclick="loadSampleData('edge')">Load Edge Case Data</button>
            <button class="btn btn-success" onclick="loadSampleData('real')">Load Real Failed Submission Data</button>
        </div>

        <div class="grid">
            <!-- Form Data Input -->
            <div class="test-section">
                <h3>📝 Test Form Data</h3>
                <div class="form-group">
                    <label>First Name:</label>
                    <input type="text" id="firstName" value="">
                </div>
                <div class="form-group">
                    <label>Second Name:</label>
                    <input type="text" id="secondName" value="">
                </div>
                <div class="form-group">
                    <label>Third Name:</label>
                    <input type="text" id="thirdName" value="">
                </div>
                <div class="form-group">
                    <label>Mobile:</label>
                    <input type="text" id="Mobile" value="">
                </div>
                <div class="form-group">
                    <label>National ID:</label>
                    <input type="text" id="National_ID" value="">
                </div>
                <div class="form-group">
                    <label>ID Type:</label>
                    <select id="ID_Type">
                        <option value="national">National</option>
                        <option value="passport">Passport</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Age:</label>
                    <input type="number" id="Age" value="">
                </div>
                <div class="form-group">
                    <label>Gender:</label>
                    <select id="Gender">
                        <option value="">Select Gender</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Religion:</label>
                    <input type="text" id="Religion" value="">
                </div>
                <div class="form-group">
                    <label>Social Status:</label>
                    <input type="text" id="Social_Status" value="">
                </div>
                <div class="form-group">
                    <label>Address:</label>
                    <input type="text" id="Address" value="">
                </div>
                <div class="form-group">
                    <label>City:</label>
                    <input type="text" id="City" value="">
                </div>
                <div class="form-group">
                    <label>University:</label>
                    <select id="University">
                        <option value="">Loading universities...</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Faculty:</label>
                    <select id="Faculty">
                        <option value="">Loading faculties...</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Scientific Degree:</label>
                    <select id="Scientific_Degree" onchange="loadSpecializations()">
                        <option value="">Loading scientific degrees...</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Specialization:</label>
                    <select id="Specialization">
                        <option value="">Select scientific degree first</option>
                    </select>
                    <button type="button" class="btn btn-info" onclick="loadSpecializations()" style="margin-left: 10px; padding: 5px 10px;">Reload Specializations</button>
                </div>
                <div class="form-group">
                    <label>Grade:</label>
                    <input type="text" id="Grade" value="">
                </div>
                <div class="form-group">
                    <label>Year of Graduation:</label>
                    <input type="number" id="Year_Of_Graduation" value="">
                </div>
                <div class="form-group">
                    <label>Department (Main Position):</label>
                    <select id="Main_Position">
                        <option value="">Loading departments...</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Section:</label>
                    <select id="SECTIONId">
                        <option value="">Loading sections...</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Position:</label>
                    <select id="Sub_Position" onchange="onPositionChange()">
                        <option value="">Loading positions...</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Expected Salary:</label>
                    <input type="number" id="Expected_Salary" value="">
                </div>
                <div class="form-group">
                    <label>English Level:</label>
                    <input type="text" id="English_Level" value="">
                </div>
                <div class="form-group">
                    <label>Computer Skills:</label>
                    <input type="text" id="Computer_Skills" value="">
                </div>
                <div class="form-group">
                    <label>Work at Shifa:</label>
                    <select id="Work_At_Shifa">
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Chronic Diseases:</label>
                    <select id="Chronic_Diseases">
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Relative in Hospital:</label>
                    <select id="Relative_In_Hospital">
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>

            <!-- Test Controls -->
            <div class="test-section">
                <h3>🧪 Test Controls</h3>
                <button class="btn btn-primary" onclick="testValidation()">1. Test Form Validation</button>
                <button class="btn btn-primary" onclick="testIdResolution()">2. Test ID Resolution</button>
                <button class="btn btn-primary" onclick="testDataMapping()">3. Test Data Mapping</button>
                <button class="btn btn-success" onclick="testFullSubmission()">4. Test Full Submission</button>
                <button class="btn btn-info" onclick="verifyDatabase()">5. Verify Database Record</button>
                <br><br>
                <button class="btn btn-warning" onclick="clearResults()">Clear All Results</button>
                <button class="btn btn-info" onclick="exportTestData()">Export Test Data</button>
                <button class="btn btn-info" onclick="debugFormState()">Debug Form State</button>
            </div>
        </div>

        <!-- Results Section -->
        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        // Global variables for dropdown data
        let universitiesData = [];
        let facultiesData = [];
        let scientificDegreesData = [];
        let specializationsData = {};
        let departmentsData = [];
        let sectionsData = [];
        let positionsData = [];

        // Sample data sets - Updated to work with dropdown selections
        const sampleData = {
            valid: {
                firstName: "Ahmed", secondName: "Mohamed", thirdName: "Ali",
                Mobile: "01012345678", National_ID: "29001010101234", ID_Type: "national",
                Age: 30, Gender: "Male", Religion: "Muslim", Social_Status: "Single",
                Address: "123 Test Street", City: "Cairo",
                University: "", Faculty: "", Scientific_Degree: "", Specialization: "", // Will be set by dropdown selection
                Grade: "Good", Year_Of_Graduation: 2020, Sub_Position: "Software Engineer",
                Expected_Salary: 15000, English_Level: "Good", Computer_Skills: "Excellent",
                Work_At_Shifa: "No", Chronic_Diseases: "No", Relative_In_Hospital: "No"
            },
            invalid: {
                firstName: "", secondName: "Test", thirdName: "User",
                Mobile: "123", National_ID: "123", ID_Type: "national",
                Age: 150, Gender: "", Religion: "", Social_Status: "",
                Address: "", City: "", University: "", Faculty: "",
                Scientific_Degree: "", Specialization: "", Grade: "",
                Year_Of_Graduation: 1800, Sub_Position: "", Expected_Salary: -100,
                English_Level: "", Computer_Skills: "", Work_At_Shifa: "",
                Chronic_Diseases: "", Relative_In_Hospital: ""
            },
            edge: {
                firstName: "Test", secondName: "Edge", thirdName: "Case",
                Mobile: "01000000000", National_ID: "12345678901234", ID_Type: "national",
                Age: 16, Gender: "Male", Religion: "Other", Social_Status: "Married",
                Address: "Edge Case Address", City: "Alexandria",
                University: "", Faculty: "", Scientific_Degree: "", Specialization: "",
                Grade: "Pass", Year_Of_Graduation: 2024, Sub_Position: "Intern",
                Expected_Salary: 1, English_Level: "Beginner", Computer_Skills: "Basic",
                Work_At_Shifa: "Yes", Chronic_Diseases: "Yes", Relative_In_Hospital: "Yes"
            },
            real: {
                firstName: "Delilah", secondName: "Bowman", thirdName: "Genevieve Sweet",
                Mobile: "01053274558", National_ID: "29002220103275", ID_Type: "national",
                Age: "30", Gender: "Female", Religion: "Other", Social_Status: "Widowed", // Fixed: Added Age and Gender
                Address: "Atque eum harum maxi", City: "Sharqia", // Fixed: Address field name
                University: "79", Faculty: "66", Scientific_Degree: "14", Specialization: "155", // Real IDs from failed submission
                Grade: "Excellent", Year_Of_Graduation: 2005,
                Main_Position: "10303", SECTIONId: "1030301", Sub_Position: "باريستا", // Real department and section IDs
                Expected_Salary: 20, English_Level: "Fluent", Computer_Skills: "Very Good",
                Work_At_Shifa: "Yes", Chronic_Diseases: "No", Relative_In_Hospital: "Yes"
            }
        };

        // Load dropdown data from API
        async function loadDropdownData() {
            try {
                // Load universities
                const universitiesResponse = await fetch('/test/api/universities');
                universitiesData = await universitiesResponse.json();
                populateUniversityDropdown();

                // Load faculties
                const facultiesResponse = await fetch('/test/api/faculties');
                facultiesData = await facultiesResponse.json();
                populateFacultyDropdown();

                // Load scientific degrees
                const degreesResponse = await fetch('/test/api/scientific-degrees');
                scientificDegreesData = await degreesResponse.json();
                populateScientificDegreeDropdown();

                // Load departments
                const departmentsResponse = await fetch('/test/api/departments');
                departmentsData = await departmentsResponse.json();
                populateDepartmentDropdown();

                // Load sections
                const sectionsResponse = await fetch('/test/api/sections');
                sectionsData = await sectionsResponse.json();
                populateSectionDropdown();

                // Load positions
                const positionsResponse = await fetch('/test/api/positions');
                positionsData = await positionsResponse.json();
                populatePositionDropdown();

                addResult('info', 'Dropdown data loaded successfully');
            } catch (error) {
                addResult('error', \`Failed to load dropdown data: \${error.message}\`);
            }
        }

        function populateUniversityDropdown() {
            const select = document.getElementById('University');
            select.innerHTML = '<option value="">Select University</option>';
            universitiesData.forEach(uni => {
                const option = document.createElement('option');
                option.value = uni.uni_id;
                option.textContent = uni.uniname_en || uni.uniname_ar || \`University \${uni.uni_id}\`;
                select.appendChild(option);
            });
        }

        function populateFacultyDropdown() {
            const select = document.getElementById('Faculty');
            select.innerHTML = '<option value="">Select Faculty</option>';
            facultiesData.forEach(faculty => {
                const option = document.createElement('option');
                option.value = faculty.fac_id;
                option.textContent = faculty.facname_en || faculty.facname_ar || \`Faculty \${faculty.fac_id}\`;
                select.appendChild(option);
            });
        }

        function populateScientificDegreeDropdown() {
            const select = document.getElementById('Scientific_Degree');
            select.innerHTML = '<option value="">Select Scientific Degree</option>';
            scientificDegreesData.forEach(degree => {
                const option = document.createElement('option');
                option.value = degree.Category_ID;
                option.textContent = degree.CategoryName_EN || degree.CategoryName_AR || \`Degree \${degree.Category_ID}\`;
                select.appendChild(option);
            });
        }

        function populateDepartmentDropdown() {
            const select = document.getElementById('Main_Position');
            select.innerHTML = '<option value="">Select Department</option>';
            departmentsData.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.DepartmentId;
                option.textContent = dept.DepartmentName || \`Department \${dept.DepartmentId}\`;
                select.appendChild(option);
            });
        }

        function populateSectionDropdown() {
            const select = document.getElementById('SECTIONId');
            select.innerHTML = '<option value="">Select Section</option>';
            sectionsData.forEach(section => {
                const option = document.createElement('option');
                option.value = section.SECTIONId;
                option.textContent = section.SECTIONName || \`Section \${section.SECTIONId}\`;
                select.appendChild(option);
            });
        }

        function populatePositionDropdown() {
            const select = document.getElementById('Sub_Position');
            select.innerHTML = '<option value="">Select Position</option>';
            positionsData.forEach(position => {
                const option = document.createElement('option');
                option.value = position.PositionName;
                option.textContent = position.PositionName || \`Position \${position.HIMS_ID}\`;
                // Store additional data as data attributes for cascading
                option.setAttribute('data-department-id', position.DepartmentId || '');
                option.setAttribute('data-section-id', position.SECTIONId || '');
                option.setAttribute('data-hims-id', position.HIMS_ID || '');
                select.appendChild(option);
            });
        }

        // Cascading function: Auto-select department and section when position is selected
        function onPositionChange() {
            const positionSelect = document.getElementById('Sub_Position');
            const departmentSelect = document.getElementById('Main_Position');
            const sectionSelect = document.getElementById('SECTIONId');

            const selectedOption = positionSelect.options[positionSelect.selectedIndex];

            if (selectedOption && selectedOption.value) {
                // Get the department and section IDs from the selected position
                const departmentId = selectedOption.getAttribute('data-department-id');
                const sectionId = selectedOption.getAttribute('data-section-id');
                const himsId = selectedOption.getAttribute('data-hims-id');

                // Auto-select department
                if (departmentId) {
                    departmentSelect.value = departmentId;
                    addResult('info', \`Auto-selected Department ID: \${departmentId}\`);
                }

                // Auto-select section
                if (sectionId) {
                    sectionSelect.value = sectionId;
                    addResult('info', \`Auto-selected Section ID: \${sectionId}\`);
                }

                addResult('info', \`Position selected: "\${selectedOption.value}" (HIMS_ID: \${himsId})\`);
            } else {
                // Clear department and section if no position selected
                departmentSelect.value = '';
                sectionSelect.value = '';
                addResult('info', 'Position cleared - Department and Section reset');
            }
        }

        async function loadSpecializations() {
            const categoryId = document.getElementById('Scientific_Degree').value;
            const specializationSelect = document.getElementById('Specialization');

            if (!categoryId) {
                specializationSelect.innerHTML = '<option value="">Select scientific degree first</option>';
                return;
            }

            try {
                specializationSelect.innerHTML = '<option value="">Loading specializations...</option>';
                const response = await fetch(\`/test/api/specializations/\${categoryId}\`);
                const specializations = await response.json();

                specializationSelect.innerHTML = '<option value="">Select Specialization</option>';
                specializations.forEach(spec => {
                    const option = document.createElement('option');
                    option.value = spec.Grade_ID;
                    option.textContent = spec.GradeName_EN || spec.GradeName_AR || \`Specialization \${spec.Grade_ID}\`;
                    specializationSelect.appendChild(option);
                });

                // Store specializations data for this category
                specializationsData[categoryId] = specializations;

            } catch (error) {
                specializationSelect.innerHTML = '<option value="">Error loading specializations</option>';
                addResult('error', \`Failed to load specializations: \${error.message}\`);
            }
        }

        async function loadSampleData(type) {
            const data = sampleData[type];

            // First load all non-dependent fields
            Object.keys(data).forEach(key => {
                if (key !== 'Specialization') { // Skip specialization for now
                    const element = document.getElementById(key);
                    if (element) {
                        element.value = data[key] || '';
                    }
                }
            });

            // Then handle specialization after scientific degree is set
            if (data.Scientific_Degree && data.Specialization) {
                // Wait for scientific degree to be set, then load specializations
                setTimeout(async () => {
                    await loadSpecializations();
                    // After specializations are loaded, set the specialization value
                    setTimeout(() => {
                        const specializationElement = document.getElementById('Specialization');
                        if (specializationElement) {
                            specializationElement.value = data.Specialization;
                            addResult('info', \`Set specialization to: \${data.Specialization}\`);
                        }
                    }, 200);
                }, 100);
            }

            addResult('info', \`Loaded \${type} sample data\`);
        }

        // Helper function to get dropdown display names for debugging
        function getDropdownDisplayNames(formData) {
            const displayNames = { ...formData };

            // Get university name
            if (formData.University) {
                const uni = universitiesData.find(u => u.uni_id == formData.University);
                displayNames.University_Name = uni ? (uni.uniname_en || uni.uniname_ar) : 'Unknown University';
            }

            // Get faculty name
            if (formData.Faculty) {
                const faculty = facultiesData.find(f => f.fac_id == formData.Faculty);
                displayNames.Faculty_Name = faculty ? (faculty.facname_en || faculty.facname_ar) : 'Unknown Faculty';
            }

            // Get scientific degree name
            if (formData.Scientific_Degree) {
                const degree = scientificDegreesData.find(d => d.Category_ID == formData.Scientific_Degree);
                displayNames.Scientific_Degree_Name = degree ? (degree.CategoryName_EN || degree.CategoryName_AR) : 'Unknown Degree';
            }

            // Get specialization name
            if (formData.Specialization && formData.Scientific_Degree) {
                const specs = specializationsData[formData.Scientific_Degree];
                if (specs) {
                    const spec = specs.find(s => s.Grade_ID == formData.Specialization);
                    displayNames.Specialization_Name = spec ? (spec.GradeName_EN || spec.GradeName_AR) : 'Unknown Specialization';
                }
            }

            // Get department name
            if (formData.Main_Position) {
                const dept = departmentsData.find(d => d.DepartmentId == formData.Main_Position);
                displayNames.Department_Name = dept ? dept.DepartmentName : 'Unknown Department';
            }

            // Get section name
            if (formData.SECTIONId) {
                const section = sectionsData.find(s => s.SECTIONId == formData.SECTIONId);
                displayNames.Section_Name = section ? section.SECTIONName : 'Unknown Section';
            }

            // Get position details
            if (formData.Sub_Position) {
                const position = positionsData.find(p => p.PositionName == formData.Sub_Position);
                if (position) {
                    displayNames.Position_HIMS_ID = position.HIMS_ID;
                    displayNames.Position_Department = position.DepartmentName;
                    displayNames.Position_Section = position.SECTIONName;
                }
            }

            return displayNames;
        }

        function getFormData() {
            const formData = {};
            const inputs = document.querySelectorAll('input, select');
            inputs.forEach(input => {
                if (input.id) {
                    formData[input.id] = input.value;
                }
            });
            return formData;
        }

        async function testValidation() {
            const formData = getFormData();
            const displayNames = getDropdownDisplayNames(formData);
            addResult('info', 'Testing form validation...');
            addResult('info', \`Form Data with Display Names:\\n\${JSON.stringify(displayNames, null, 2)}\`);

            try {
                const response = await fetch('/test/validate-form-data', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                const result = await response.json();
                addResult(result.success ? 'success' : 'error',
                    \`Validation Result:\\n\${JSON.stringify(result, null, 2)}\`);
            } catch (error) {
                addResult('error', \`Validation Error: \${error.message}\`);
            }
        }

        async function testIdResolution() {
            const formData = getFormData();
            addResult('info', 'Testing ID resolution...');

            try {
                const response = await fetch('/test/resolve-ids', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                const result = await response.json();
                addResult(result.success ? 'success' : 'error',
                    \`ID Resolution Result:\\n\${JSON.stringify(result, null, 2)}\`);
            } catch (error) {
                addResult('error', \`ID Resolution Error: \${error.message}\`);
            }
        }

        async function testDataMapping() {
            const formData = getFormData();
            addResult('info', 'Testing data mapping...');

            try {
                const response = await fetch('/test/map-data', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                const result = await response.json();
                addResult(result.success ? 'success' : 'error',
                    \`Data Mapping Result:\\n\${JSON.stringify(result, null, 2)}\`);
            } catch (error) {
                addResult('error', \`Data Mapping Error: \${error.message}\`);
            }
        }

        async function testFullSubmission() {
            const formData = getFormData();
            addResult('info', 'Testing full submission...');

            try {
                const response = await fetch('/test/full-submission', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                const result = await response.json();
                addResult(result.success ? 'success' : 'error',
                    \`Full Submission Result:\\n\${JSON.stringify(result, null, 2)}\`);
            } catch (error) {
                addResult('error', \`Full Submission Error: \${error.message}\`);
            }
        }

        async function verifyDatabase() {
            const candidateId = prompt('Enter Candidate ID to verify:');
            if (!candidateId) return;

            addResult('info', \`Verifying database record for Candidate ID: \${candidateId}\`);

            try {
                const response = await fetch(\`/test/verify-database/\${candidateId}\`);
                const result = await response.json();
                addResult(result.success ? 'success' : 'error',
                    \`Database Verification Result:\\n\${JSON.stringify(result, null, 2)}\`);
            } catch (error) {
                addResult('error', \`Database Verification Error: \${error.message}\`);
            }
        }

        function addResult(type, message) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = \`result-box result-\${type}\`;
            resultDiv.textContent = \`[\${new Date().toLocaleTimeString()}] \${message}\`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function exportTestData() {
            const formData = getFormData();
            const dataStr = JSON.stringify(formData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'test-form-data.json';
            link.click();
        }

        function debugFormState() {
            const formData = getFormData();
            const displayNames = getDropdownDisplayNames(formData);

            addResult('info', 'Current Form State Debug:');
            addResult('info', \`Form Data:\\n\${JSON.stringify(formData, null, 2)}\`);
            addResult('info', \`Display Names:\\n\${JSON.stringify(displayNames, null, 2)}\`);

            // Check critical fields
            const criticalFields = ['Age', 'Gender', 'Religion', 'Social_Status', 'Address', 'Scientific_Degree', 'Specialization', 'Main_Position', 'SECTIONId'];
            addResult('info', 'Critical Fields Check:');
            criticalFields.forEach(field => {
                const value = formData[field];
                const status = value && value.trim() !== '' ? '✅' : '❌';
                addResult('info', \`  \${field}: \${status} "\${value}"\`);
            });

            // Check dropdown states
            const scientificDegreeSelect = document.getElementById('Scientific_Degree');
            const specializationSelect = document.getElementById('Specialization');
            const positionSelect = document.getElementById('Sub_Position');
            const departmentSelect = document.getElementById('Main_Position');
            const sectionSelect = document.getElementById('SECTIONId');

            addResult('info', 'Dropdown States:');
            addResult('info', \`  Scientific Degree: "\${scientificDegreeSelect.value}" (options: \${scientificDegreeSelect.options.length})\`);
            addResult('info', \`  Specialization: "\${specializationSelect.value}" (options: \${specializationSelect.options.length})\`);
            addResult('info', \`  Position: "\${positionSelect.value}" (options: \${positionSelect.options.length})\`);
            addResult('info', \`  Department: "\${departmentSelect.value}" (options: \${departmentSelect.options.length})\`);
            addResult('info', \`  Section: "\${sectionSelect.value}" (options: \${sectionSelect.options.length})\`);

            if (specializationSelect.options.length <= 1) {
                addResult('error', 'Specialization dropdown not loaded! Click "Reload Specializations" button.');
            }

            if (positionSelect.options.length <= 1) {
                addResult('error', 'Position dropdown not loaded! Check API connection.');
            }
        }

        // Load dropdown data and sample data on page load
        window.onload = async () => {
            await loadDropdownData();
            loadSampleData('valid');
        };
    </script>
</body>
</html>`;

    res.send(testPageHTML);
});

// Test endpoint 1: Validate form data
app.post('/test/validate-form-data', async (req, res) => {
    console.log('\n🧪 TEST: Form Validation');
    console.log('Input data:', JSON.stringify(req.body, null, 2));

    try {
        const validation = validateFormData(req.body);
        console.log('Validation result:', validation);

        res.json({
            success: validation.success,
            errors: validation.errors,
            inputData: req.body,
            step: 'validation'
        });
    } catch (error) {
        console.error('Validation test error:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            step: 'validation'
        });
    }
});

// Test endpoint 2: Resolve IDs
app.post('/test/resolve-ids', async (req, res) => {
    console.log('\n🧪 TEST: ID Resolution');
    console.log('Input data:', JSON.stringify(req.body, null, 2));

    let pool;
    try {
        pool = await sql.connect(config);
        const resolvedIds = await resolveFormDataIds(pool, req.body);
        console.log('Resolved IDs:', resolvedIds);

        res.json({
            success: true,
            resolvedIds: resolvedIds,
            inputData: req.body,
            step: 'id_resolution'
        });
    } catch (error) {
        console.error('ID resolution test error:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            step: 'id_resolution'
        });
    } finally {
        if (pool) await pool.close();
    }
});

// Test endpoint 3: Map data
app.post('/test/map-data', async (req, res) => {
    console.log('\n🧪 TEST: Data Mapping');
    console.log('Input data:', JSON.stringify(req.body, null, 2));

    let pool;
    try {
        pool = await sql.connect(config);
        const resolvedIds = await resolveFormDataIds(pool, req.body);
        const mappedData = mapFormDataToDatabase(req.body, resolvedIds);
        console.log('Mapped data:', mappedData);

        res.json({
            success: true,
            inputData: req.body,
            resolvedIds: resolvedIds,
            mappedData: mappedData,
            step: 'data_mapping'
        });
    } catch (error) {
        console.error('Data mapping test error:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            step: 'data_mapping'
        });
    } finally {
        if (pool) await pool.close();
    }
});

// Test endpoint 4: Full submission
app.post('/test/full-submission', async (req, res) => {
    console.log('\n🧪 TEST: Full Submission');
    console.log('Input data:', JSON.stringify(req.body, null, 2));

    try {
        const result = await processBilingualFormSubmission(req.body);
        console.log('Full submission result:', result);

        res.json(result);
    } catch (error) {
        console.error('Full submission test error:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            step: 'full_submission'
        });
    }
});

// API endpoints for test interface dropdowns
app.get('/test/api/universities', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT uni_id, uniname_ar, uniname_en
            FROM [SHMS].[dbo].[University]
            WHERE uni_id > 0 AND (uniname_ar IS NOT NULL OR uniname_en IS NOT NULL)
            ORDER BY uniname_en, uniname_ar
        `);
        res.json(result.recordset);
    } catch (error) {
        console.error('Error fetching universities for test:', error);
        res.status(500).json({ error: 'Failed to fetch universities' });
    } finally {
        if (pool) await pool.close();
    }
});

app.get('/test/api/faculties', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT fac_id, facname_ar, facname_en
            FROM [SHMS].[dbo].[Faculty]
            WHERE fac_id > 0 AND (facname_ar IS NOT NULL OR facname_en IS NOT NULL)
            ORDER BY facname_en, facname_ar
        `);
        res.json(result.recordset);
    } catch (error) {
        console.error('Error fetching faculties for test:', error);
        res.status(500).json({ error: 'Failed to fetch faculties' });
    } finally {
        if (pool) await pool.close();
    }
});

app.get('/test/api/scientific-degrees', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT DISTINCT Category_ID, CategoryName_AR, CategoryName_EN
            FROM [SHMS].[dbo].[Qualifications]
            WHERE Category_ID IS NOT NULL AND (CategoryName_AR IS NOT NULL OR CategoryName_EN IS NOT NULL)
            ORDER BY CategoryName_EN, CategoryName_AR
        `);
        res.json(result.recordset);
    } catch (error) {
        console.error('Error fetching scientific degrees for test:', error);
        res.status(500).json({ error: 'Failed to fetch scientific degrees' });
    } finally {
        if (pool) await pool.close();
    }
});

app.get('/test/api/specializations/:categoryId', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const categoryId = parseInt(req.params.categoryId);

        if (isNaN(categoryId)) {
            return res.status(400).json({ error: 'Invalid category ID' });
        }

        const result = await pool.request()
            .input('categoryId', sql.Int, categoryId)
            .query(`
                SELECT Grade_ID, GradeName_AR, GradeName_EN, Category_ID
                FROM [SHMS].[dbo].[Qualifications]
                WHERE Category_ID = @categoryId
                AND Grade_ID IS NOT NULL
                AND (GradeName_AR IS NOT NULL OR GradeName_EN IS NOT NULL)
                ORDER BY GradeName_EN, GradeName_AR
            `);
        res.json(result.recordset);
    } catch (error) {
        console.error('Error fetching specializations for test:', error);
        res.status(500).json({ error: 'Failed to fetch specializations' });
    } finally {
        if (pool) await pool.close();
    }
});

app.get('/test/api/departments', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT DepartmentId, DepartmentName
            FROM [SHMS].[dbo].[Departments]
            WHERE DepartmentId > 0 AND DepartmentName IS NOT NULL AND IsActive = 1
            ORDER BY DepartmentName
        `);
        res.json(result.recordset);
    } catch (error) {
        console.error('Error fetching departments for test:', error);
        res.status(500).json({ error: 'Failed to fetch departments' });
    } finally {
        if (pool) await pool.close();
    }
});

app.get('/test/api/sections', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT SECTIONId, SECTIONName
            FROM [SHMS].[dbo].[SECTIONS]
            WHERE SECTIONId > 0 AND SECTIONName IS NOT NULL
            ORDER BY SECTIONName
        `);
        res.json(result.recordset);
    } catch (error) {
        console.error('Error fetching sections for test:', error);
        res.status(500).json({ error: 'Failed to fetch sections' });
    } finally {
        if (pool) await pool.close();
    }
});

app.get('/test/api/positions', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request().query(`
            SELECT
                JP.HIMS_ID,
                JP.PositionName,
                JP.DepartmentId,
                JP.SECTIONId,
                D.DepartmentName,
                S.SECTIONName
            FROM [SHMS].[dbo].[JobPositions] JP
            LEFT JOIN [SHMS].[dbo].[Departments] D ON JP.DepartmentId = D.DepartmentId
            LEFT JOIN [SHMS].[dbo].[SECTIONS] S ON JP.SECTIONId = S.SECTIONId
            WHERE JP.IsActive = 1
            AND JP.PositionName IS NOT NULL
            ORDER BY JP.PositionName
        `);
        res.json(result.recordset);
    } catch (error) {
        console.error('Error fetching positions for test:', error);
        res.status(500).json({ error: 'Failed to fetch positions' });
    } finally {
        if (pool) await pool.close();
    }
});

// Test endpoint 5: Verify database record
app.get('/test/verify-database/:candidateId', async (req, res) => {
    console.log('\n🧪 TEST: Database Verification');
    const candidateId = req.params.candidateId;
    console.log('Candidate ID:', candidateId);

    let pool;
    try {
        pool = await sql.connect(config);
        const result = await pool.request()
            .input('candidateId', sql.Int, parseInt(candidateId))
            .query('SELECT * FROM [SHMS].[dbo].[Candidates] WHERE CandidateId = @candidateId');

        if (result.recordset.length === 0) {
            res.json({
                success: false,
                error: 'Candidate not found',
                candidateId: candidateId
            });
            return;
        }

        const candidate = result.recordset[0];
        console.log('Database record:', candidate);

        // Analyze NULL fields
        const nullFields = [];
        const nonNullFields = [];
        Object.entries(candidate).forEach(([key, value]) => {
            if (value === null || value === undefined) {
                nullFields.push(key);
            } else {
                nonNullFields.push({ field: key, value: value });
            }
        });

        res.json({
            success: true,
            candidateId: candidateId,
            databaseRecord: candidate,
            analysis: {
                nullFields: nullFields,
                nonNullFields: nonNullFields,
                totalFields: Object.keys(candidate).length,
                nullCount: nullFields.length,
                nonNullCount: nonNullFields.length
            }
        });
    } catch (error) {
        console.error('Database verification test error:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            candidateId: candidateId
        });
    } finally {
        if (pool) await pool.close();
    }
});

// Add route for live application form
app.get('/live', async (req, res) => {
    let pool;
    try {
        pool = await sql.connect(config);

        // Get departments for positions
        const departmentsQuery = `
            SELECT DISTINCT
                DepartmentId,
                DepartmentName
            FROM [SHMS].[dbo].[Departments]
            WHERE IsActive = 1
            ORDER BY DepartmentName
        `;

        const departments = await pool.request().query(departmentsQuery);

        res.render('live', {
            departments: departments.recordset
        });
    } catch (err) {
        console.error('Database error:', err);
        res.status(500).send('Server Error');
    } finally {
        if (pool) {
            try {
                await pool.close();
            } catch (err) {
                console.error('Error closing connection:', err);
            }
        }
    }
});

const PORT = process.env.PORT || 50;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});