/**
 * Phone Call Handler
 * Manages phone call form interactions and status updates
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get references to form elements
    const outcomeSelect = document.getElementById('call-outcome');
    const saveCallBtn = document.querySelector('.save-call-btn');
    const callForm = document.getElementById('phone-call-form');
    const durationButtons = document.querySelectorAll('.duration-btn');
    const durationInput = document.getElementById('call-duration');
    
    console.log('Phone Call Handler initialized');
    
    // Setup duration buttons
    if (durationButtons && durationButtons.length > 0) {
        durationButtons.forEach(button => {
            button.addEventListener('click', function() {
                const duration = this.getAttribute('data-duration');
                if (durationInput) {
                    durationInput.value = duration;
                }
            });
        });
    }
    
    // Function to update the status in the call history table
    function updateStatusInCallHistory(callId, status) {
        // Find the call row in the table
        const callRow = document.querySelector(`tr[data-call-id="${callId}"]`);
        if (callRow) {
            // Find the status cell (3rd column)
            const statusCell = callRow.querySelector('td:nth-child(3)');
            if (statusCell) {
                statusCell.textContent = status;
                console.log('Updated status in call history table');
            }
        }
    }
    
    // Helper function to show notifications
    function showNotification(message, type = 'info') {
        // Check if notification system exists
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                text: message,
                icon: type,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        } else {
            // Fallback to alert for simple implementation
            if (type === 'error') {
                console.error(message);
            } else {
                console.log(message);
            }
        }
    }
}); 