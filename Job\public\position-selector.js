// Position selector helper
(function() {
    console.log("Position selector script loaded");
    
    // Load our CSS
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '/position-fix.css';
    document.head.appendChild(link);
    
    /**
     * Select a position by its text content
     * 
     * @param {string} positionText - The text of the position to select
     * @returns {boolean} - True if selection was successful, false otherwise
     */
    window.selectPositionByText = function(positionText) {
        if (!positionText || positionText.trim() === '') {
            console.error("Position text is empty");
            return false;
        }
        
        console.log(`Attempting to select position: "${positionText}"`);
        
        // Make sure DOM is fully loaded
        if (document.readyState !== 'complete') {
            console.log("DOM not fully loaded, waiting...");
            window.addEventListener('load', function() {
                window.selectPositionByText(positionText);
            });
            return false;
        }
        
        // First try direct selection from select element (easiest method)
        const positionSelect = document.getElementById('position');
        if (positionSelect) {
            console.log("Trying to select directly from #position select dropdown");
            
            for (let i = 0; i < positionSelect.options.length; i++) {
                const option = positionSelect.options[i];
                // Case insensitive match
                if (option.text && option.text.toLowerCase().includes(positionText.toLowerCase())) {
                    positionSelect.value = option.value;
                    
                    // Trigger change event
                    const event = new Event('change', { bubbles: true });
                    positionSelect.dispatchEvent(event);
                    
                    console.log(`Selected position from select dropdown: ${option.text} (value: ${option.value})`);
                    return true;
                }
            }
        }
        
        // Try to find position items in the positions list (for visual UI)
        const positionItems = document.querySelectorAll('.position-item, .positions-list div');
        
        if (positionItems && positionItems.length > 0) {
            console.log(`Found ${positionItems.length} position items to check`);
            
            // Try exact match first
            for (const item of positionItems) {
                if (item.textContent.includes(positionText)) {
                    console.log(`Found exact match in position item: "${item.textContent.trim()}"`);
                    // Click the item to trigger selection
                    item.click();
                    return true;
                }
            }
            
            // If no exact match, try partial match
            for (const item of positionItems) {
                const itemText = item.textContent.toLowerCase();
                const searchText = positionText.toLowerCase();
                
                if (itemText.includes(searchText)) {
                    console.log(`Found partial match in position item: "${item.textContent.trim()}"`);
                    // Click the item to trigger selection
                    item.click();
                    return true;
                }
            }
        }
        
        // Get all position containers (original method)
        const positionContainers = document.querySelectorAll('.position-container');
        
        if (positionContainers && positionContainers.length > 0) {
            console.log(`Found ${positionContainers.length} position containers`);
            
            // First try exact match
            for (const container of positionContainers) {
                if (container.textContent.includes(positionText)) {
                    console.log(`Found exact match for "${positionText}"`);
                    return selectPosition(container);
                }
            }
            
            // If no exact match, try partial match
            console.log("No exact match found, trying partial match");
            for (const container of positionContainers) {
                const containerText = container.textContent.toLowerCase();
                const searchText = positionText.toLowerCase();
                
                if (containerText.includes(searchText)) {
                    console.log(`Found partial match for "${positionText}"`);
                    return selectPosition(container);
                }
            }
        }
        
        // If nothing worked, try using the PositionHandler class which should be available globally
        if (window.positionHandler) {
            console.log("Using PositionHandler class from window.positionHandler");
            // Create a position search input and set its value
            const positionSearch = document.getElementById('positionSearch');
            if (positionSearch) {
                positionSearch.value = positionText;
                // Trigger input event to filter positions
                const inputEvent = new Event('input', { bubbles: true });
                positionSearch.dispatchEvent(inputEvent);
                
                // Give time for filtering to complete, then try to click the first visible item
                setTimeout(() => {
                    const firstVisibleItem = document.querySelector('.position-item:not([style*="display: none"])');
                    if (firstVisibleItem) {
                        console.log(`Clicking first visible position item after search: ${firstVisibleItem.textContent.trim()}`);
                        firstVisibleItem.click();
                        return true;
                    }
                }, 300);
            }
        }
        
        console.error(`No position found matching "${positionText}"`);
        return false;
    };
    
    /**
     * Select a position by clicking on its container
     * 
     * @param {Element} container - The position container element
     * @returns {boolean} - True if selection was successful, false otherwise
     */
    function selectPosition(container) {
        if (!container) {
            console.error("Invalid container provided");
            return false;
        }
        
        // Clear previous selections
        const selectedContainers = document.querySelectorAll('.position-container.selected');
        for (const selected of selectedContainers) {
            selected.classList.remove('selected');
        }
        
        // Add selected class
        container.classList.add('selected');
        
        // Get position ID
        const positionId = container.getAttribute('data-id');
        if (!positionId) {
            console.warn("Position container has no data-id attribute, trying fallback methods...");
            
            // Try to extract position details from the text content
            const positionText = container.textContent.trim();
            console.log(`Using position text: "${positionText}"`);
            
            // Try to update standard position select if it exists
            const positionSelect = document.getElementById('position');
            if (positionSelect) {
                // Try to find an option with matching text
                let found = false;
                for (const option of positionSelect.options) {
                    if (option.text && option.text.includes(positionText)) {
                        positionSelect.value = option.value;
                        
                        // Trigger change event
                        const event = new Event('change', { bubbles: true });
                        positionSelect.dispatchEvent(event);
                        
                        console.log(`Selected position from dropdown: ${option.text}`);
                        found = true;
                        break;
                    }
                }
                
                if (!found) {
                    // Just click the element as a last resort
                    console.log("No matching option found, just clicking the container element");
                    container.click();
                }
                
                return true;
            } else {
                // Just click the element as a last resort
                console.log("No position select found, just clicking the container element");
                container.click();
                return true;
            }
        }
        
        console.log(`Selected position ID: ${positionId}`);
        
        // Update hidden input if it exists
        const hiddenInput = document.querySelector('input[name="PositionId"]');
        if (hiddenInput) {
            hiddenInput.value = positionId;
        }
        
        // Update select element if it exists
        const selectElement = document.querySelector('select[name="PositionId"]');
        if (selectElement) {
            selectElement.value = positionId;
            
            // Trigger change event
            const event = new Event('change', { bubbles: true });
            selectElement.dispatchEvent(event);
        }
        
        // Trigger click event on the container
        container.click();
        
        return true;
    }
    
    // Attach click handlers to all position containers
    document.addEventListener('DOMContentLoaded', function() {
        const containers = document.querySelectorAll('.position-container');
        for (const container of containers) {
            container.addEventListener('click', function() {
                selectPosition(this);
            });
        }
    });
})();

// Add reliable position selection code
function selectPositionDirectly(positionId, positionName, departmentId, departmentName, sectionId, sectionName) {
    console.log('Direct position selection with:', { 
        positionId, 
        positionName, 
        departmentId, 
        departmentName, 
        sectionId, 
        sectionName 
    });
    
    // Set hidden form values
    const positionSelect = document.getElementById('position');
    if (positionSelect) {
        // Ensure the option exists
        let option = Array.from(positionSelect.options).find(opt => opt.value == positionId);
        
        if (!option) {
            // Create the option if it doesn't exist
            option = document.createElement('option');
            option.value = positionId;
            option.text = positionName;
            positionSelect.appendChild(option);
        }
        
        // Select the option
        option.selected = true;
        
        // Trigger change event
        const event = new Event('change');
        positionSelect.dispatchEvent(event);
    }
    
    // Set department display and hidden value
    const departmentInput = document.getElementById('department');
    const departmentIdInput = document.getElementById('departmentId');
    if (departmentInput && departmentIdInput) {
        departmentInput.value = departmentName;
        departmentIdInput.value = departmentId;
    }
    
    // Set section display and hidden value
    const sectionInput = document.getElementById('section');
    const sectionIdInput = document.getElementById('sectionId');
    if (sectionInput && sectionIdInput) {
        sectionInput.value = sectionName;
        sectionIdInput.value = sectionId;
    }
    
    // Update validation icons
    updateFormValidation();
    
    console.log('Position selection completed');
    return true;
}

// Add a function to update validation icons
function updateFormValidation() {
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
        if (input.value) {
            input.classList.add('is-valid');
            input.classList.remove('is-invalid');
        } else {
            input.classList.add('is-invalid');
            input.classList.remove('is-valid');
        }
    });
}

// Override the problematic selectPosition function
const originalSelectPosition = window.selectPosition;
window.selectPosition = function(positionItem) {
    console.log('Enhanced selectPosition called with:', positionItem);
    
    try {
        // Try the original function first
        const result = originalSelectPosition(positionItem);
        
        // Fallback to direct selection if needed
        if (!result && positionItem) {
            const positionId = positionItem.getAttribute('data-id') || positionItem.id;
            const positionName = positionItem.querySelector('.position-title')?.textContent.trim() || '';
            
            // Extract department and section info
            const detailsElement = positionItem.querySelector('.position-details');
            const detailsText = detailsElement?.textContent || '';
            
            // Parse the details text to extract department and section
            const departmentMatch = detailsText.match(/Department:\s*([^,\n]+)/);
            const sectionMatch = detailsText.match(/Section:\s*([^,\n]+)/);
            
            const departmentName = departmentMatch ? departmentMatch[1].trim() : '';
            const sectionName = sectionMatch ? sectionMatch[1].trim() : '';
            
            // Get IDs from data attributes if available
            const departmentId = positionItem.getAttribute('data-department-id') || '';
            const sectionId = positionItem.getAttribute('data-section-id') || '';
            
            return selectPositionDirectly(positionId, positionName, departmentId, departmentName, sectionId, sectionName);
        }
        
        return result;
    } catch (error) {
        console.error('Error in selectPosition:', error);
        return false;
    }
};

// Add a function to manually set position by name
window.forcePositionSelection = function(positionName) {
    console.log('Forcing position selection:', positionName);
    
    // Find position with this name in dropdown options
    const positionSelect = document.getElementById('position');
    if (positionSelect) {
        const options = Array.from(positionSelect.options);
        for (const option of options) {
            if (option.text.trim() === positionName.trim()) {
                option.selected = true;
                const positionId = option.value;
                
                // Only fetch additional details if we have a numeric ID
                if (positionId && !isNaN(parseInt(positionId))) {
                    // Update hidden values based on the selected position
                    fetch(`/api/position/${positionId}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            selectPositionDirectly(
                                data.HIMS_ID,
                                data.PositionName,
                                data.DepartmentId,
                                data.DepartmentName,
                                data.SECTIONId,
                                data.SECTIONName
                            );
                        })
                        .catch(error => {
                            console.error('Error fetching position details:', error);
                            // Still mark the select as having a valid choice
                            positionSelect.classList.add('is-valid');
                            positionSelect.classList.remove('is-invalid');
                        });
                } else {
                    console.log('Position found but no valid ID available for API call');
                    // Still mark the select as having a valid choice
                    positionSelect.classList.add('is-valid');
                    positionSelect.classList.remove('is-invalid');
                }
                
                // Trigger change event
                const event = new Event('change');
                positionSelect.dispatchEvent(event);
                
                return true;
            }
        }
    }
    
    // If position not found in dropdown, try to search all available positions
    if (window.positionHandler && window.positionHandler.positions) {
        const allPositions = window.positionHandler.positions;
        const matchingPosition = allPositions.find(pos => 
            pos.PositionName.trim() === positionName.trim()
        );
        
        if (matchingPosition) {
            console.log('Position found in available positions:', matchingPosition);
            if (window.positionHandler.selectPosition) {
                window.positionHandler.selectPosition(matchingPosition);
                return true;
            }
        }
    }
    
    console.warn('Position not found:', positionName);
    return false;
}; 