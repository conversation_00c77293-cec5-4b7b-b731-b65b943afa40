.toggle {
  position: relative;
  width: 60px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(0px);
  border-radius: 20px;
  padding: 4px;
  cursor: pointer;
  z-index: 100;
}
.toggle img {
  position: absolute;
  top: 4px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
  transition: all 0.2s ease-in-out;
}
.toggle img.en {
  left: 4px;
}
.toggle img.fr {
  left: 34px;
  transform: rotate(360deg);
}

.noselect {
  -webkit-touch-callout: none;
  /* iOS Safari */
  -webkit-user-select: none;
  /* Safari */
  -khtml-user-select: none;
  /* Konqueror HTML */
  -moz-user-select: none;
  /* Old versions of Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  /* Non-prefixed version, currently
     supported by Chrome, Edge, Opera and Firefox */
}

.toggle {
  transform: scale(2);
}

body {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  background-color: #93E5AB;
  margin: 0;
  padding: 0;
}