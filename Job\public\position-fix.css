/* Style for selected position */
.position-container.selected {
    background-color: #e2f0ff !important;
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Ensure position container is clickable */
.position-container {
    cursor: pointer !important;
    transition: all 0.3s ease;
    position: relative;
}

/* Add highlight on hover */
.position-container:hover {
    background-color: #f0f9f6 !important;
}

.position-container.selected::after {
    content: "✓";
    position: absolute;
    right: 10px;
    top: 10px;
    color: #0d6efd;
    font-weight: bold;
    font-size: 20px;
} 