{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAMlC,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\ndeclare global {\n  interface Event {}\n}\n\nexport { AbortError } from \"./AbortError.js\";\nexport { AbortSignalLike } from \"./AbortSignalLike.js\";\n"]}