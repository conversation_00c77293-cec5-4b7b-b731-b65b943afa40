2025-04-18 11:57:46 info: [App] Server running on port 50
2025-04-18 11:57:48 info: [Email] Email server connection successful
2025-04-18 11:57:48 info: [App] Email server connection verified successfully
2025-04-18 11:58:40 info: [CandidateService] Received save-candidate request
{
  "nationalId": "55555555555666",
  "isTokenUpdate": false,
  "tokenValue": "None"
}
2025-04-18 11:58:40 info: [CandidateService] Record does not exist, will create new
2025-04-18 11:58:40 info: [CandidateService] Creating new candidate record
2025-04-18 11:58:40 info: [CandidateService] Candidate saved to database successfully
{
  "candidateId": 158
}
2025-04-18 12:03:37 info: [App] Server running on port 50
2025-04-18 12:03:39 info: [Email] Email server connection successful
2025-04-18 12:03:39 info: [App] Email server connection verified successfully
2025-04-18 12:03:41 info: [App] Server running on port 50
2025-04-18 12:03:43 info: [Email] Email server connection successful
2025-04-18 12:03:43 info: [App] Email server connection verified successfully
2025-04-18 12:03:47 info: [App] Server running on port 50
2025-04-18 12:03:49 info: [Email] Email server connection successful
2025-04-18 12:03:49 info: [App] Email server connection verified successfully
2025-04-18 12:03:52 info: [App] Server running on port 50
2025-04-18 12:03:54 info: [Email] Email server connection successful
2025-04-18 12:03:54 info: [App] Email server connection verified successfully
2025-04-18 12:03:55 info: [App] Server running on port 50
2025-04-18 12:03:58 info: [App] Server running on port 50
2025-04-18 12:03:59 info: [Email] Email server connection successful
2025-04-18 12:03:59 info: [App] Email server connection verified successfully
2025-04-18 12:04:29 info: [App] Server running on port 50
2025-04-18 12:04:31 info: [Email] Email server connection successful
2025-04-18 12:04:31 info: [App] Email server connection verified successfully
2025-04-18 12:04:33 info: [App] Server running on port 50
2025-04-18 12:04:35 info: [Email] Email server connection successful
2025-04-18 12:04:35 info: [App] Email server connection verified successfully
2025-04-18 12:04:37 info: [App] Server running on port 50
2025-04-18 12:04:39 info: [Email] Email server connection successful
2025-04-18 12:04:39 info: [App] Email server connection verified successfully
2025-04-18 12:04:51 info: [App] Server running on port 50
2025-04-18 12:04:53 info: [Email] Email server connection successful
2025-04-18 12:04:53 info: [App] Email server connection verified successfully
2025-04-18 12:04:58 info: [App] Server running on port 50
2025-04-18 12:05:00 info: [Email] Email server connection successful
2025-04-18 12:05:00 info: [App] Email server connection verified successfully
2025-04-18 12:05:46 info: [App] Server running on port 50
2025-04-18 12:05:48 info: [Email] Email server connection successful
2025-04-18 12:05:48 info: [App] Email server connection verified successfully
2025-04-18 12:06:33 info: [App] Server running on port 50
2025-04-18 12:06:35 info: [Email] Email server connection successful
2025-04-18 12:06:35 info: [App] Email server connection verified successfully
2025-04-18 12:06:38 info: [App] Server running on port 50
2025-04-18 12:06:40 info: [Email] Email server connection successful
2025-04-18 12:06:40 info: [App] Email server connection verified successfully
2025-04-18 12:21:53 info: [CandidateService] Received save-candidate request
{
  "nationalId": "12345678952365",
  "isTokenUpdate": false,
  "tokenValue": "None"
}
2025-04-18 12:21:53 info: [CandidateService] Record does not exist, will create new
2025-04-18 12:21:53 info: [CandidateService] Creating new candidate record
2025-04-18 12:21:53 info: [CandidateService] Candidate saved to database successfully
{
  "candidateId": 159
}
2025-04-18 12:57:20 info: [App] Server running on port 50
2025-04-18 12:57:53 info: [Email] Email server connection successful
2025-04-18 12:57:53 info: [App] Email server connection verified successfully
2025-04-18 13:01:17 info: [App] Server running on port 50
2025-04-18 13:01:22 info: [App] Server running on port 50
2025-04-18 13:01:27 info: [App] Server running on port 50
2025-04-18 13:01:29 info: [App] Server running on port 50
2025-04-18 13:01:33 info: [App] Server running on port 50
2025-04-18 13:01:36 info: [App] Server running on port 50
2025-04-18 13:01:38 info: [App] Server running on port 50
2025-04-18 13:01:41 info: [App] Server running on port 50
2025-04-18 13:01:43 info: [Email] Email server connection successful
2025-04-18 13:01:43 info: [App] Email server connection verified successfully
2025-04-18 13:01:57 info: [App] Server running on port 50
2025-04-18 13:01:59 info: [Email] Email server connection successful
2025-04-18 13:01:59 info: [App] Email server connection verified successfully
2025-04-18 13:02:05 info: [App] Server running on port 50
2025-04-18 13:02:07 info: [Email] Email server connection successful
2025-04-18 13:02:07 info: [App] Email server connection verified successfully
2025-04-18 13:02:10 info: [App] Server running on port 50
2025-04-18 13:02:12 info: [Email] Email server connection successful
2025-04-18 13:02:12 info: [App] Email server connection verified successfully
2025-04-18 13:02:19 info: [App] Server running on port 50
2025-04-18 13:02:20 info: [Email] Email server connection successful
2025-04-18 13:02:20 info: [App] Email server connection verified successfully
2025-04-18 13:02:26 info: [App] Server running on port 50
2025-04-18 13:02:28 info: [App] Server running on port 50
2025-04-18 13:02:30 info: [Email] Email server connection successful
2025-04-18 13:02:30 info: [App] Email server connection verified successfully
2025-04-18 13:02:36 info: [App] Server running on port 50
2025-04-18 13:02:38 info: [Email] Email server connection successful
2025-04-18 13:02:38 info: [App] Email server connection verified successfully
2025-04-18 13:02:41 info: [App] Server running on port 50
2025-04-18 13:02:43 info: [App] Server running on port 50
2025-04-18 13:02:45 info: [App] Server running on port 50
2025-04-18 13:02:47 info: [Email] Email server connection successful
2025-04-18 13:02:47 info: [App] Email server connection verified successfully
2025-04-18 13:05:06 info: [CandidateService] Received save-candidate request
{
  "nationalId": "22222222223333",
  "isTokenUpdate": false,
  "tokenValue": "None"
}
2025-04-18 13:05:06 info: [CandidateService] Record does not exist, will create new
2025-04-18 13:05:06 info: [CandidateService] Creating new candidate record
2025-04-18 13:05:06 info: [CandidateService] Candidate saved to database successfully
{
  "candidateId": 160
}
2025-04-18 13:09:01 info: [App] Server running on port 50
2025-04-18 13:09:02 info: [Email] Email server connection successful
2025-04-18 13:09:02 info: [App] Email server connection verified successfully
2025-04-18 13:09:18 info: [App] Server running on port 50
2025-04-18 13:09:21 info: [App] Server running on port 50
2025-04-18 13:09:22 info: [Email] Email server connection successful
2025-04-18 13:09:22 info: [App] Email server connection verified successfully
2025-04-18 13:09:36 info: [App] Server running on port 50
2025-04-18 13:09:36 info: [Redis] Connected to Redis server
2025-04-18 13:09:38 info: [Email] Email server connection successful
2025-04-18 13:09:38 info: [App] Email server connection verified successfully
2025-04-18 13:09:50 info: [App] Server running on port 50
2025-04-18 13:09:50 info: [Redis] Connected to Redis server
2025-04-18 13:09:52 info: [App] Server running on port 50
2025-04-18 13:09:52 info: [Redis] Connected to Redis server
2025-04-18 13:09:54 info: [Email] Email server connection successful
2025-04-18 13:09:54 info: [App] Email server connection verified successfully
2025-04-18 13:10:06 info: [App] Server running on port 50
2025-04-18 13:10:06 info: [Redis] Connected to Redis server
2025-04-18 13:10:08 info: [Email] Email server connection successful
2025-04-18 13:10:08 info: [App] Email server connection verified successfully
2025-04-18 13:10:12 info: [App] Server running on port 50
2025-04-18 13:10:12 info: [Redis] Connected to Redis server
2025-04-18 13:10:14 info: [Email] Email server connection successful
2025-04-18 13:10:14 info: [App] Email server connection verified successfully
2025-04-18 13:10:16 info: [App] Server running on port 50
2025-04-18 13:10:16 info: [Redis] Connected to Redis server
2025-04-18 13:10:18 info: [Email] Email server connection successful
2025-04-18 13:10:18 info: [App] Email server connection verified successfully
2025-04-18 13:10:20 info: [App] Server running on port 50
2025-04-18 13:10:20 info: [Redis] Connected to Redis server
2025-04-18 13:10:22 info: [Email] Email server connection successful
2025-04-18 13:10:22 info: [App] Email server connection verified successfully
2025-04-18 13:10:29 info: [App] Server running on port 50
2025-04-18 13:10:29 info: [Redis] Connected to Redis server
2025-04-18 13:10:31 info: [Email] Email server connection successful
2025-04-18 13:10:31 info: [App] Email server connection verified successfully
2025-04-18 13:10:33 info: [App] Server running on port 50
2025-04-18 13:10:33 info: [Redis] Connected to Redis server
2025-04-18 13:10:35 info: [App] Server running on port 50
2025-04-18 13:10:35 info: [Redis] Connected to Redis server
2025-04-18 13:10:37 info: [App] Server running on port 50
2025-04-18 13:10:37 info: [Redis] Connected to Redis server
2025-04-18 13:10:39 info: [Email] Email server connection successful
2025-04-18 13:10:39 info: [App] Email server connection verified successfully
2025-04-18 13:17:23 info: [App] Server running on port 50
2025-04-18 13:17:23 info: [Redis] Connected to Redis server
2025-04-18 13:17:25 info: [App] Server running on port 50
2025-04-18 13:17:25 info: [Redis] Connected to Redis server
2025-04-18 13:17:27 info: [Email] Email server connection successful
2025-04-18 13:17:27 info: [App] Email server connection verified successfully
2025-04-18 13:17:34 info: [App] Server running on port 50
2025-04-18 13:17:34 info: [Redis] Connected to Redis server
2025-04-18 13:17:36 info: [App] Server running on port 50
2025-04-18 13:17:36 info: [Redis] Connected to Redis server
2025-04-18 13:17:38 info: [Email] Email server connection successful
2025-04-18 13:17:38 info: [App] Email server connection verified successfully
2025-04-18 13:17:51 info: [App] Server running on port 50
2025-04-18 13:17:51 info: [Redis] Connected to Redis server
2025-04-18 13:17:53 info: [App] Server running on port 50
2025-04-18 13:17:53 info: [Redis] Connected to Redis server
2025-04-18 13:17:55 info: [Email] Email server connection successful
2025-04-18 13:17:55 info: [App] Email server connection verified successfully
2025-04-18 13:18:11 error: [Database] Database query error
{
  "error": "Cannot read properties of undefined (reading 'on')",
  "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at D:\\Project\\Prod\\job_app\\node_modules\\mssql\\lib\\tedious\\request.js:442:20\n    at D:\\Project\\Prod\\job_app\\node_modules\\mssql\\lib\\base\\connection-pool.js:289:41\n    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n    at runNextTicks (node:internal/process/task_queues:65:3)\n    at processImmediate (node:internal/timers:437:9)"
}
2025-04-18 13:21:45 info: [Database] Initializing SQL Server connection pool
2025-04-18 13:21:45 info: [App] Server running on port 50
2025-04-18 13:21:45 info: [Redis] Connected to Redis server
2025-04-18 13:21:45 info: [Database] SQL Server connection pool established
2025-04-18 13:21:47 info: [Email] Email server connection successful
2025-04-18 13:21:47 info: [App] Email server connection verified successfully
2025-04-18 13:22:10 info: [Database] Initializing SQL Server connection pool
2025-04-18 13:22:10 info: [App] Server running on port 50
2025-04-18 13:22:10 info: [Redis] Connected to Redis server
2025-04-18 13:22:10 info: [Database] SQL Server connection pool established
2025-04-18 13:22:10 info: [App] Database connection verified successfully
2025-04-18 13:22:12 info: [Database] Initializing SQL Server connection pool
2025-04-18 13:22:12 info: [App] Server running on port 50
2025-04-18 13:22:12 info: [Redis] Connected to Redis server
2025-04-18 13:22:12 info: [Database] SQL Server connection pool established
2025-04-18 13:22:12 info: [App] Database connection verified successfully
2025-04-18 13:22:14 info: [Email] Email server connection successful
2025-04-18 13:22:14 info: [App] Email server connection verified successfully
2025-04-18 13:22:42 info: [Database] Initializing SQL Server connection pool
2025-04-18 13:22:43 info: [App] Server running on port 50
2025-04-18 13:22:43 info: [Redis] Connected to Redis server
2025-04-18 13:22:43 info: [Database] SQL Server connection pool established
2025-04-18 13:22:43 info: [App] Database connection verified successfully
2025-04-18 13:22:45 info: [Database] Initializing SQL Server connection pool
2025-04-18 13:22:45 info: [App] Server running on port 50
2025-04-18 13:22:45 info: [Redis] Connected to Redis server
2025-04-18 13:22:45 info: [Database] SQL Server connection pool established
2025-04-18 13:22:45 info: [App] Database connection verified successfully
2025-04-18 13:22:47 info: [Email] Email server connection successful
2025-04-18 13:22:47 info: [App] Email server connection verified successfully
2025-04-18 13:22:54 info: [Database] Initializing SQL Server connection pool
2025-04-18 13:22:54 info: [App] Server running on port 50
2025-04-18 13:22:54 info: [Redis] Connected to Redis server
2025-04-18 13:22:54 info: [Database] SQL Server connection pool established
2025-04-18 13:22:54 info: [App] Database connection verified successfully
2025-04-18 13:22:56 info: [Database] Initializing SQL Server connection pool
2025-04-18 13:22:56 info: [App] Server running on port 50
2025-04-18 13:22:56 info: [Redis] Connected to Redis server
2025-04-18 13:22:56 info: [Database] SQL Server connection pool established
2025-04-18 13:22:56 info: [App] Database connection verified successfully
2025-04-18 13:22:58 info: [Email] Email server connection successful
2025-04-18 13:22:58 info: [App] Email server connection verified successfully
2025-04-18 13:23:09 info: [Database] Initializing SQL Server connection pool
2025-04-18 13:23:09 info: [App] Server running on port 50
2025-04-18 13:23:09 info: [Redis] Connected to Redis server
2025-04-18 13:23:09 info: [Database] SQL Server connection pool established
2025-04-18 13:23:09 info: [App] Database connection verified successfully
2025-04-18 13:23:11 info: [Email] Email server connection successful
2025-04-18 13:23:11 info: [App] Email server connection verified successfully
2025-04-18 13:24:10 info: [Routes] Processing candidate submission
{
  "hasToken": false,
  "nationalId": "6666****"
}
2025-04-18 13:24:10 info: [Redis] Created submission lock for key: 66666699999999
{
  "expirationSeconds": 60
}
2025-04-18 13:24:10 info: [CandidateService] Received save-candidate request
{
  "nationalId": "66666699999999",
  "isTokenUpdate": false,
  "tokenValue": "None"
}
2025-04-18 13:24:10 info: [CandidateService] Record does not exist, will create new
2025-04-18 13:24:10 info: [CandidateService] Creating new candidate record
2025-04-18 13:24:10 info: [CandidateService] Candidate saved to database successfully
{
  "candidateId": 161
}
2025-04-18 13:24:10 info: [Redis] Released submission lock for key: 66666699999999
2025-04-18 13:24:10 info: [Routes] Submission lock released
{
  "lockKey": "6666****"
}
2025-04-18 13:30:14 info: [CandidateService] Searching for candidate with token
{
  "token": "4e9c684b425c957f63ffbcc0b1e6c88c91aa9421"
}
2025-04-18 13:31:13 info: [Routes] Processing candidate submission
{
  "hasToken": true,
  "nationalId": "5555****"
}
2025-04-18 13:31:13 info: [Redis] Created submission lock for key: 4e9c684b425c957f63ffbcc0b1e6c88c91aa9421
{
  "expirationSeconds": 60
}
2025-04-18 13:31:13 info: [CandidateService] Received save-candidate request
{
  "nationalId": "55555555555569",
  "isTokenUpdate": true,
  "tokenValue": "4e9c684b425c957f63ffbcc0b1e6c88c91aa9421"
}
2025-04-18 13:31:13 info: [CandidateService] Record exists with ID
{
  "candidateId": 162
}
2025-04-18 13:31:13 info: [CandidateService] Updating existing candidate record
{
  "candidateId": 162
}
2025-04-18 13:31:13 info: [Redis] Released submission lock for key: 4e9c684b425c957f63ffbcc0b1e6c88c91aa9421
2025-04-18 13:31:13 info: [Routes] Submission lock released
{
  "lockKey": "4e9c****"
}
2025-04-18 13:37:08 info: [CandidateService] Searching for candidate with token
{
  "token": "4e9c684b425c957f63ffbcc0b1e6c88c91aa9421"
}
2025-04-18 13:37:57 info: [Routes] Processing candidate submission
{
  "hasToken": true,
  "nationalId": "7777****"
}
2025-04-18 13:37:57 info: [Redis] Created submission lock for key: 4e9c684b425c957f63ffbcc0b1e6c88c91aa9421
{
  "expirationSeconds": 60
}
2025-04-18 13:37:57 info: [CandidateService] Received save-candidate request
{
  "nationalId": "77777777777777",
  "isTokenUpdate": true,
  "tokenValue": "4e9c684b425c957f63ffbcc0b1e6c88c91aa9421"
}
2025-04-18 13:37:57 info: [CandidateService] Record exists with ID
{
  "candidateId": 162
}
2025-04-18 13:37:57 info: [CandidateService] Updating existing candidate record
{
  "candidateId": 162
}
2025-04-18 13:37:57 info: [Redis] Released submission lock for key: 4e9c684b425c957f63ffbcc0b1e6c88c91aa9421
2025-04-18 13:37:57 info: [Routes] Submission lock released
{
  "lockKey": "4e9c****"
}
2025-04-18 13:47:47 info: [App] Server running on port 50
2025-04-18 13:47:49 info: [App] Server running on port 50
2025-04-18 13:47:51 info: [Email] Email server connection successful
2025-04-18 13:47:51 info: [App] Email server connection verified successfully
2025-04-18 13:47:54 info: [CandidateService] Searching for candidate with token
{
  "token": "4e9c684b425c957f63ffbcc0b1e6c88c91aa9421"
}
2025-04-18 13:48:37 info: [CandidateService] Received save-candidate request
{
  "nationalId": "77777777777777",
  "isTokenUpdate": true,
  "tokenValue": "4e9c684b425c957f63ffbcc0b1e6c88c91aa9421"
}
2025-04-18 13:48:37 info: [CandidateService] Record exists with ID
{
  "candidateId": 162
}
2025-04-18 13:48:37 info: [CandidateService] Updating existing candidate record
{
  "candidateId": 162
}
2025-04-18 13:49:09 info: [App] Server running on port 50
2025-04-18 13:49:10 info: [App] Server running on port 50
2025-04-18 13:49:12 info: [Email] Email server connection successful
2025-04-18 13:49:12 info: [App] Email server connection verified successfully
2025-04-18 13:49:45 info: [CandidateService] Received save-candidate request
{
  "nationalId": "52323232323323",
  "isTokenUpdate": true,
  "tokenValue": "4e9c684b425c957f63ffbcc0b1e6c88c91aa9421"
}
2025-04-18 13:49:45 info: [CandidateService] Record exists with ID
{
  "candidateId": 162
}
2025-04-18 13:49:45 info: [CandidateService] Updating existing candidate record
{
  "candidateId": 162
}
