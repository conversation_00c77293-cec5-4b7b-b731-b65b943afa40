<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Shifa Hospital Job Application</title>
  <link rel="stylesheet" href="/style.css">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="icon" type="image/png" href="Images/favicon.png">
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
  <style>
    /* Custom Select2 Styles */
    .select2-container--default .select2-selection--single {
      height: 45px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      background-color: #f8f9fa;
      transition: all 0.3s ease;
    }
    
    .select2-container--default .select2-selection--single:focus,
    .select2-container--default.select2-container--open .select2-selection--single {
      border-color: #008D5C;
      box-shadow: 0 0 0 3px rgba(0, 141, 92, 0.1);
      background-color: #fff;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
      line-height: 45px;
      padding-left: 15px;
      padding-right: 30px;
      color: #333;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
      height: 43px;
      width: 30px;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow b {
      border-color: #008D5C transparent transparent transparent;
      border-width: 6px 4px 0 4px;
    }
    
    .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
      border-color: transparent transparent #008D5C transparent;
      border-width: 0 4px 6px 4px;
    }
    
    .select2-container--default .select2-search--dropdown .select2-search__field {
      padding: 10px 15px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
    }
    
    .select2-container--default .select2-search--dropdown .select2-search__field:focus {
      outline: none;
      border-color: #008D5C;
    }
    
    .select2-dropdown {
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .select2-results__option {
      padding: 10px 15px;
      transition: background-color 0.2s ease;
    }
    
    .select2-container--default .select2-results__option--highlighted[aria-selected] {
      background-color: #008D5C;
      color: white;
    }
    
    /* RTL Support */
    [dir="rtl"] .select2-container--default .select2-selection--single .select2-selection__rendered {
      padding-right: 15px;
      padding-left: 30px;
      text-align: right;
    }
    
    [dir="rtl"] .select2-container--default .select2-selection--single .select2-selection__arrow {
      left: 1px;
      right: auto;
    }

    /* Modern form styling */
    .input-container input, 
    .input-container select,
    .form-group input[readonly] {
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      padding: 12px 15px 12px 40px;
      transition: all 0.3s ease;
      background-color: #f8f9fa;
      font-size: 16px;
    }

    .input-container input:focus, 
    .input-container select:focus {
      border-color: #008D5C;
      box-shadow: 0 0 0 3px rgba(0, 141, 92, 0.1);
      background-color: #fff;
      outline: none;
    }

    /* Field icons styling */
    .input-container::before {
      color: #008D5C;
      font-size: 16px;
      z-index: 2;
    }

    /* Section headers styling */
    section h4 {
      margin-bottom: 25px;
      padding-bottom: 10px;
      border-bottom: 2px solid #f0f0f0;
    }

    /* Section styling */
    section {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    /* RTL specific styles */
    [dir="rtl"] .input-container input,
    [dir="rtl"] .input-container select,
    [dir="rtl"] .form-group input[readonly] {
      padding: 12px 40px 12px 15px;
      text-align: right;
    }

    /* Updated validation styling */
    .validation-icon {
      width: 24px;
      height: 24px;
    }

    /* Progress indicator enhancement */
    #svg_form_time circle,
    #svg_form_time rect {
      fill: #008D5C;
      transition: all 0.3s ease;
    }
    
    #svg_form_time circle:hover,
    #svg_form_time rect:hover {
      fill: #006B44;
      transform: scale(1.05);
    }

    /* Navigation buttons */
    .navigation-buttons {
      margin-top: 10px;
      margin-bottom: 30px;
    }

    .button {
      font-size: 16px;
      transition: all 0.3s ease;
    }

    .button:hover {
      background: #006B44;
      transform: translateY(-2px);
      box-shadow: 0px 4px 8px rgba(0,0,0,0.2);
    }

    /* Custom file input styling */
    .custom-file-wrapper {
      margin-top: 15px;
    }

    .custom-file-label {
      background-color: #f8f9fa;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      padding: 12px 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    .custom-file-label:hover {
      border-color: #008D5C;
      background-color: #fff;
    }

    /* Name fields styling */
    .name-fields {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 15px;
    }

    .name-field {
      position: relative;
    }

    .name-field label {
      display: block;
      margin-bottom: 8px;
      color: #666;
      font-size: 0.9em;
    }

    .name-field label i {
      color: #008D5C;
      margin-right: 5px;
    }

    .name-field input {
      width: 100%;
      padding: 12px 15px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      transition: all 0.3s ease;
      background-color: #f8f9fa;
    }

    .name-field input:focus {
      border-color: #008D5C;
      box-shadow: 0 0 0 3px rgba(0, 141, 92, 0.1);
      background-color: #fff;
      outline: none;
    }

    [dir="rtl"] .name-field label i {
      margin-right: 0;
      margin-left: 5px;
    }

    [dir="rtl"] .name-field input {
      text-align: right;
    }

    /* Responsive name fields */
    @media (max-width: 768px) {
      .name-fields {
        grid-template-columns: 1fr;
      }
    }

    /* Input container label styling */
    .input-container label {
      display: block;
      margin-bottom: 8px;
      color: #666;
      font-size: 0.9em;
      font-weight: 500;
    }

    .input-container label i {
      color: #008D5C;
      margin-right: 5px;
      width: 18px;
      text-align: center;
    }

    [dir="rtl"] .input-container label i {
      margin-right: 0;
      margin-left: 5px;
    }

    /* Remove redundant icon display */
    .input-container::before {
      display: none;
    }

    /* Improved layout and spacing */
    section {
      padding: 30px;
      margin: 0 auto 25px;
      max-width: 1000px;
    }

    .input-container {
      margin-bottom: 25px;
    }

    section h4 {
      font-size: 1.3em;
      margin-top: 0;
      margin-bottom: 30px;
      padding-bottom: 12px;
      position: relative;
    }

    section h4::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 3px;
      background-color: #008D5C;
      border-radius: 3px;
    }

    /* Card-like sections with hover effect */
    section {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    section:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }

    /* Page header styling */
    h1.header-title {
      margin-bottom: 10px;
      color: #008D5C;
      font-weight: 600;
    }

    /* Validation icons spacing */
    .validation-icon {
      right: 15px;
    }

    [dir="rtl"] .validation-icon {
      right: auto;
      left: 15px;
    }

    .position-search {
        width: 100%;
        padding: 8px;
        margin-top: -10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .position-search:focus {
        border-color: #008D5C;
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 141, 92, 0.2);
    }

    /* RTL support for search input */
    body[dir="rtl"] .position-search {
        text-align: right;
    }

    /* Mobile Optimization */
    @media screen and (max-width: 767px) {
      html, body {
        font-size: 16px;
        touch-action: manipulation;
        -webkit-text-size-adjust: 100%;
      }
      
      section {
        padding: 15px 10px;
        margin-bottom: 15px;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box;
      }
      
      .name-fields {
        grid-template-columns: 1fr;
      }
      
      .input-container input, 
      .input-container select,
      .form-group input[readonly],
      .button {
        height: 48px;
        min-height: 48px;
        font-size: 16px;
        touch-action: manipulation;
      }
      
      #svg_wrap {
        transform: scale(0.8);
        margin: -20px 0;
      }
      
      .navigation-buttons {
        display: flex;
        justify-content: space-between;
        padding: 0 10px;
      }
      
      .button {
        padding: 12px 20px;
        min-width: 120px;
      }
      
      /* Fix Select2 on mobile */
      .select2-container--default .select2-selection--single {
        height: 48px;
      }
      
      .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 48px;
      }
      
      .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 46px;
      }
      
      /* Fix input zooming */
      input[type="text"],
      input[type="email"],
      input[type="tel"],
      input[type="number"],
      select {
        font-size: 16px !important;
      }
    }

    /* Updated Notification Bar */
    .notification-bar {
      position: fixed;
      top: 135px;
      left: 310px;
      width: 50%;
      background-color: #d4edda;
      color: #155724;
      padding: 13px;
      text-align: center;
      border: 1px solid #c3e6cb;
      border-radius: 5px;
      z-index: 1000;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .close-notification {
      margin-left: 20px;
      cursor: pointer;
      font-weight: bold;
    }

    .new-button {
      background: #28a745;
      margin-left: 10px;
    }

    .new-button:hover {
      background: #218838;
    }

    .submit-container {
      display: flex;
      justify-content: center;
      gap: 10px;
      flex-wrap: wrap;
    }

    .submitting {
      opacity: 0.7;
      cursor: not-allowed;
    }

    /* Styles for readonly position info */
    .readonly-position-info {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px dashed #e9ecef;
    }

    .info-row:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }

    .info-label {
      font-weight: bold;
      color: #495057;
      min-width: 120px;
    }

    .info-label i {
      margin-right: 8px;
      color: #008D5C;
    }

    .info-value {
      flex: 1;
      color: #212529;
      font-size: 1.05em;
    }

    /* Style for Expected Salary when accessed via token */
    .position-section .salary-container {
      margin-top: 15px;
    }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
<header class="header">
  <!-- <img src="" alt="Logo" class="logo"> -->
  <h1 class="header-title"></h1>
  <div id="toggle" class="toggle noselect">
    <img id="flag" src="https://flagicons.lipis.dev/flags/4x3/us.svg" alt="flag" class="en">
      </div>
</header>

<div id="svg_wrap"></div>

<h1 style="font-size: 1.5em;color: #008D5C; font-family: 'Nexa Bold', sans-serif;">Shifa Hospital Job Application</h1>
<section>
  <h4 class="text-center" style="color: #008D5C;"><i class="fas fa-user"></i> Basic Information</h4>
  
  <div class="input-container name">
    <div class="name-fields">
      <div class="name-field">
        <label for="firstName"><i class="fas fa-user"></i> First Name</label>
        <input type="text" 
               id="firstName"
               name="firstName" 
               placeholder="First Name" 
               data-en-placeholder="First Name"
               data-ar-placeholder="الاسم الأول"
               required 
               pattern="^([\u0600-\u06FF]+|[A-Za-z ]+)$" 
               title="Please enter a name using either Arabic or English characters (no mixing)" />
        <span class="validation-icon"></span>
      </div>
      <div class="name-field">
        <label for="secondName"><i class="fas fa-user"></i> Second Name</label>
        <input type="text" 
               id="secondName"
               name="secondName" 
               placeholder="Second Name" 
               data-en-placeholder="Second Name"
               data-ar-placeholder="الاسم الثاني"
               required 
               pattern="^([\u0600-\u06FF]+|[A-Za-z ]+)$" 
               title="Please enter a name using either Arabic or English characters (no mixing)" />
        <span class="validation-icon"></span>
      </div>
      <div class="name-field">
        <label for="thirdName"><i class="fas fa-user"></i> Third Name</label>
        <input type="text" 
               id="thirdName"
               name="thirdName" 
               placeholder="Third Name" 
               data-en-placeholder="Third Name"
               data-ar-placeholder="الاسم الثالث"
               required 
               pattern="^([\u0600-\u06FF]+|[A-Za-z ]+)$" 
               title="Please enter a name using either Arabic or English characters (no mixing)" />
        <span class="validation-icon"></span>
      </div>
    </div>
    <div class="error-message" id="name-error">Please enter a valid name using either Arabic or English characters (no mixing)</div>
  </div>

  <div class="input-container phone">
    <label for="phoneNumber"><i class="fas fa-phone"></i> Phone Number</label>
    <input type="text" id="phoneNumber" placeholder="Phone Number" required />
    <span class="validation-icon"></span>
    <div class="error-message" id="phone-error">Please enter a valid phone number</div>
  </div>

  <div class="input-container id-type">
    <label for="idType"><i class="fas fa-id-card"></i> ID Type</label>
    <select id="idType" required>
      <option value="nationalId" selected>National ID</option>
      <option value="passport">Passport</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container id">
    <label for="nationalId"><i class="fas fa-id-card"></i> <span id="idTypeLabel">National ID</span></label>
    <input type="text" id="nationalId" placeholder="National ID" required />
    <span class="validation-icon"></span>
    <div class="error-message" id="national-id-error">National ID must be exactly 14 digits</div>
  </div>

  <div class="input-container age">
    <label for="age"><i class="fas fa-birthday-cake"></i> Age</label>
    <input type="text" id="age" placeholder="Age" readonly required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container birth">
    <label for="dateOfBirth"><i class="fas fa-calendar"></i> Birth Date</label>
    <input type="text" id="dateOfBirth" placeholder="Birth Date" readonly required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container location">
    <label for="governorate"><i class="fas fa-map-marker-alt"></i> Birth Place</label>
    <input type="text" id="governorate" placeholder="Birth Place" readonly required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container gender">
    <label for="gender"><i class="fas fa-venus-mars"></i> Gender</label>
    <input type="text" id="gender" placeholder="Gender" readonly required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container religion">
    <label for="religion"><i class="fas fa-pray"></i> Religion</label>
    <select id="religion" required>
      <option value="" disabled selected>Select Religion</option>
      <option value="Muslim">Muslim</option>
      <option value="Christian">Christian</option>
      <option value="Other">Other</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container address">
    <label for="address"><i class="fas fa-home"></i> Address</label>
    <input type="text" id="address" placeholder="Address" required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container city">
    <label for="city"><i class="fas fa-city"></i> City</label>
    <select id="city" required>
      <option value="" disabled selected>Select City</option>
      <option value="Cairo">Cairo</option>
      <option value="Alexandria">Alexandria</option>
      <option value="Port Said">Port Said</option>
      <option value="Suez">Suez</option>
      <option value="Damietta">Damietta</option>
      <option value="Dakahlia">Dakahlia</option>
      <option value="Sharqia">Sharqia</option>
      <option value="Qalyubia">Qalyubia</option>
      <option value="Kafr El Sheikh">Kafr El Sheikh</option>
      <option value="Gharbia">Gharbia</option>
      <option value="Monufia">Monufia</option>
      <option value="Beheira">Beheira</option>
      <option value="Ismailia">Ismailia</option>
      <option value="Giza">Giza</option>
      <option value="Beni Suef">Beni Suef</option>
      <option value="Faiyum">Faiyum</option>
      <option value="Minya">Minya</option>
      <option value="Asyut">Asyut</option>
      <option value="Sohag">Sohag</option>
      <option value="Qena">Qena</option>
      <option value="Aswan">Aswan</option>
      <option value="Luxor">Luxor</option>
      <option value="Red Sea">Red Sea</option>
      <option value="New Valley">New Valley</option>
      <option value="Matrouh">Matrouh</option>
      <option value="North Sinai">North Sinai</option>
      <option value="South Sinai">South Sinai</option>
      <option value="Outside Republic">Outside Republic</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container marital">
    <label for="socialStatus"><i class="fas fa-heart"></i> Marital Status</label>
    <select id="socialStatus" required>
      <option value="" disabled selected>Select Marital Status</option>
      <option value="Single">Single</option>
      <option value="Married">Married</option>
      <option value="Divorced">Divorced</option>
      <option value="Widowed">Widowed</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container military">
    <label for="conscriptionStatus"><i class="fas fa-shield-alt"></i> Military Status</label>
    <select id="conscriptionStatus" required>
      <option value="" disabled selected>Select Military Status</option>
      <option value="Completed">Completed</option>
      <option value="Exempted">Exempted</option>
      <option value="Postponed">Postponed</option>
      <option value="NotApplicable">Not Applicable</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container email">
    <label for="email"><i class="fas fa-envelope"></i> Email Address</label>
    <input type="text" id="email" placeholder="Email Address" required />
    <span class="validation-icon"></span>
  </div>

  
</section>

<section>
  <h4 class="text-center" style="color: #008D5C;"><i class="fas fa-graduation-cap"></i> Education</h4>
  
  <div class="input-container specialization">
    <label for="specialization"><i class="fas fa-book"></i> Specialization</label>
    <select id="specialization" class="select2-search" required>
      <option value="" disabled selected>Select Specialization</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container scientific-degree">
    <label for="scientificDegree"><i class="fas fa-award"></i> Scientific Degree</label>
    <input type="text" id="scientificDegree" placeholder="Scientific Degree" readonly required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container university">
    <label for="university"><i class="fas fa-university"></i> University</label>
    <select id="university" class="select2-search">
      <option value="" disabled selected>Select University</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container faculty">
    <label for="faculty"><i class="fas fa-building"></i> Faculty</label>
    <select id="faculty" class="select2-search">
      <option value="" disabled selected>Select Faculty</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container grade">
    <label for="grade"><i class="fas fa-star"></i> Grade</label>
    <select id="grade" class="select2-search">
      <option value="" disabled selected>Select Grade</option>
      <option value="Excellent">Excellent</option>
      <option value="Very Good">Very Good</option>
      <option value="Good">Good</option>
      <option value="Pass">Pass</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container graduation">
    <label for="yearOfGraduation"><i class="fas fa-calendar-alt"></i> Year of Graduation</label>
    <input type="text" 
           id="yearOfGraduation" 
           placeholder="Year of graduation" 
           pattern="\d{4}" 
           maxlength="4"
           oninput="this.value = this.value.replace(/[^0-9]/g, '')"
           required />
    <span class="validation-icon"></span>
    <div class="error-message" id="graduation-error">Please enter a valid graduation year</div>
  </div>
</section>
<section>
  <h4 class="text-center" style="color: #008D5C;"><i class="fas fa-briefcase"></i> Experience</h4>
  
  <div class="input-container employer">
    <label for="currentEmployer"><i class="fas fa-building"></i> Current Employer</label>
    <input type="text" id="currentEmployer" placeholder="Current Employer" required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container job">
    <label for="jobTitle"><i class="fas fa-id-badge"></i> Job Title</label>
    <input type="text" id="jobTitle" placeholder="Job Title" required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container date">
    <label for="from"><i class="fas fa-calendar"></i> From Date</label>
    <input type="date" id="from" placeholder="From Date" required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container date">
    <label for="to"><i class="fas fa-calendar"></i> To Date</label>
    <input type="date" id="to" placeholder="To Date" required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container salary">
    <label for="currentSalary"><i class="fas fa-money-bill-wave"></i> Current Salary</label>
    <input type="text" 
           id="currentSalary" 
           placeholder="Current Salary" 
           pattern="[0-9]*\.?[0-9]+" 
           oninput="this.value = this.value.replace(/[^0-9.]/g, '')"
           required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container reason">
    <label for="reasonForLeaving"><i class="fas fa-comment"></i> Reason for Leaving</label>
    <input type="text" id="reasonForLeaving" placeholder="Reason for leaving" required />
    <span class="validation-icon"></span>
  </div>

  <div class="input-container source">
    <label for="hearAboutUs"><i class="fas fa-bullhorn"></i> How Did You Hear About Us</label>
    <select id="hearAboutUs" required>
      <option value="" disabled selected>Hear About Us</option>
      <option value="Wuzzuf">Wuzzuf</option>
      <option value="Facebook">Facebook</option>
      <option value="Forasna">Forasna</option>
      <option value="Recommendation">Recommendation</option>
      <option value="Linkedin">Linkedin</option>
    </select>
    <span class="validation-icon"></span>
  </div>
</section>
<!-- <section>
  <h4 class="text-center" style="color: #008D5C;"><i class="fas fa-address-book"></i> Contact Information</h4>
  <div class="input-container email">
    <input type="text" placeholder="Email address" />
    <span class="validation-icon"></span>
  </div>
  <div class="input-container phone">
    <input type="text" placeholder="Phone" />
    <span class="validation-icon"></span>
  </div>
  <div class="input-container phone">
    <input type="text" placeholder="Mobile" />
    <span class="validation-icon"></span>
  </div>
</section> -->

<!-- <section>
  <h4 class="text-center" style="color: #008D5C;"><i class="fas fa-file-alt"></i> Application</h4>
  <div class="input-container birth">
    <input type="text" placeholder="Preferred entrance date" />
    <span class="validation-icon"></span>
  </div>
  <div class="input-container">
    <input type="text" placeholder="Number of people" />
    <span class="validation-icon"></span>
  </div>
</section> -->

<section class="position-section">
  <h4 class="text-center" style="color: #008D5C;"><i class="fas fa-user-tie"></i> <span class="translate" data-en="Position" data-ar="الوظيفة">Position</span></h4>
  
  <% if (typeof completionToken === 'undefined' || !completionToken) { %>
  <!-- Position selection - shown only when there's no token -->
  <div class="position-container">
    <div class="search-container">
      <div class="search-wrapper">
        <i class="fas fa-search search-icon"></i>
        <input type="text" 
               id="positionSearch" 
               class="position-search" 
               placeholder="Search for positions..." 
               autocomplete="off">
        <i class="fas fa-times search-clear"></i>
      </div>
      <div id="searchStatus" class="search-status"></div>
    </div>
    
    <div id="positionsList" class="positions-list custom-scrollbar"></div>
    
    <!-- Hidden select for form submission -->
    <select id="position" name="position" style="display: none;" required>
        <option value="">Select Position</option>
    </select>
    
    <div class="selected-position-details">
      <div class="form-group">
        <label><i class="fas fa-sitemap"></i> Section</label>
        <input type="text" id="section" readonly placeholder="Section">
        <input type="hidden" id="sectionId" name="sectionId">
      </div>
      
      <div class="form-group">
        <label><i class="fas fa-building"></i> Department</label>
        <input type="text" id="department" readonly placeholder="Department">
        <input type="hidden" id="departmentId" name="departmentId">
      </div>
    </div>
  </div>
  <% } else { %>
  <!-- When accessed with a token, position details are hidden but we keep the form fields as hidden inputs -->
  <div style="display:none;">
    <select id="position" name="position" required>
        <option value="<%= preselectedPosition %>" selected><%= preselectedPosition %></option>
    </select>
    <input type="text" id="section" readonly>
    <input type="hidden" id="sectionId" name="sectionId" value="<%= candidateData ? candidateData.SECTIONId : '' %>">
    <input type="text" id="department" readonly>
    <input type="hidden" id="departmentId" name="departmentId" value="<%= candidateData ? candidateData.Main_Position : '' %>">
  </div>
  
  <!-- Show position info for token users -->
  <div class="readonly-position-info">
    <div class="info-row">
      <span class="info-label"><i class="fas fa-briefcase"></i> Position:</span>
      <span class="info-value"><%= preselectedPosition %></span>
    </div>
    <div class="info-row">
      <span class="info-label"><i class="fas fa-building"></i> Department:</span>
      <span class="info-value"><%= candidateData ? candidateData.DepartmentName : '' %></span>
    </div>
    <div class="info-row">
      <span class="info-label"><i class="fas fa-sitemap"></i> Section:</span>
      <span class="info-value"><%= candidateData ? candidateData.SECTIONName : '' %></span>
    </div>
  </div>
  <% } %>

  <!-- Expected Salary - always visible -->
  <div class="salary-container">
    <div class="input-container salary">
      <label><i class="fas fa-money-bill-wave"></i> Expected Salary</label>
      <input type="text" 
             id="expectedSalary" 
             name="expectedSalary" 
             placeholder="Enter your expected salary" 
             pattern="[0-9]*\.?[0-9]+" 
             oninput="this.value = this.value.replace(/[^0-9.]/g, '')"
             required />
      <span class="validation-icon"></span>
      <div class="error-message" id="expected-salary-error">Please enter a valid salary amount</div>
    </div>
  </div>

  <div class="input-container file">
    <div class="custom-file-wrapper">
        <input type="file" 
               id="cvFile" 
               name="cvFile" 
               class="custom-file-input" 
               accept=".pdf,.doc,.docx" 
               data-max-size="5" />
        <label class="custom-file-label" for="cvFile">
            Upload CV (Optional)
        </label>
    </div>
    <div class="file-info" style="display: none;">
        <small class="text-muted">
            Maximum file size: 5MB<br>
            Accepted formats: PDF, DOC, DOCX
        </small>
    </div>
    <span class="validation-icon"></span>
  </div>
</section>

<section>
  <h4 class="text-center" style="color: #008D5C;"><i class="fas fa-info-circle"></i> Other Information</h4>
  
  <div class="input-container language">
    <label for="englishLevel"><i class="fas fa-language"></i> English Level</label>
    <select id="englishLevel" required>
      <option value="" disabled selected>Select English Level</option>
      <option value="Fluent">Fluent</option>
      <option value="Very Good">Very Good</option>
      <option value="Good">Good</option>
      <option value="Poor">Poor</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container computer">
    <label for="computerSkills"><i class="fas fa-laptop"></i> Computer Skills Level</label>
    <select id="computerSkills" required>
      <option value="" disabled selected>Select Computer Skills Level</option>
      <option value="Excellent">Excellent</option>
      <option value="Very Good">Very Good</option>
      <option value="Good">Good</option>
      <option value="Poor">Poor</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container hospital">
    <label for="workAtShifa"><i class="fas fa-hospital"></i> Previous Work at Shifa Hospital</label>
    <select id="workAtShifa" required>
      <option value="" disabled selected>Do you work at Shifa Hospital Before?</option>
      <option value="Yes">Yes</option>
      <option value="No">No</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container health">
    <label for="chronicDiseases"><i class="fas fa-heartbeat"></i> Chronic Diseases</label>
    <select id="chronicDiseases" required>
      <option value="" disabled selected>Do you have any chronic diseases?</option>
      <option value="Yes">Yes</option>
      <option value="No">No</option>
    </select>
    <span class="validation-icon"></span>
  </div>

  <div class="input-container relatives">
    <label for="relativeInHospital"><i class="fas fa-users"></i> Relatives in Hospital</label>
    <select id="relativeInHospital" required>
      <option value="" disabled selected>Do you have Relatives in the Hospital?</option>
      <option value="Yes">Yes</option>
      <option value="No">No</option>
    </select>
    <span class="validation-icon"></span>
  </div>
</section>

<section>
  <h4 class="text-center" style="color: #008D5C;">
    <i class="fas fa-check-circle"></i> 
    <span data-en="Review & Submit" data-ar="مراجعة وتقديم" class="translate">Review & Submit</span>
  </h4>
  
  <div class="finish-content">
    <div class="review-section">
      <h3 class="translate" data-en="Application Summary" data-ar="ملخص الطلب">Application Summary</h3>
      
      <div id="applicationSummary" class="summary-grid">
        <!-- Summary will be populated by JavaScript -->
      </div>
    </div>

    <div class="submit-container">
      <button id="submitBtn" class="submit-button" type="button" onclick="submitForm()">
        <span id="submitText"><i class="fas fa-paper-plane"></i> Submit Application</span>
        <span id="submitSpinner" style="display:none;"><i class="fas fa-spinner fa-spin"></i></span>
      </button>
    </div>
  </div>
</section>

<!-- <section>
  <p>General conditions</p>
  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
</section> -->
<div class="navigation-buttons">
  <button class="button" id="prev">&larr; Previous</button>
  <button class="button" id="next">Next &rarr;</button>
  <!-- <button class="button" id="submit">Agree and send application</button> -->
</div>

<script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.4.1/jquery.min.js'></script>
<script>
  // Check if jQuery is loaded
  if (typeof jQuery !== 'undefined') {
    console.log('jQuery is loaded, version:', jQuery.fn.jquery);
  } else {
    console.error('jQuery is NOT loaded!');
  }
</script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="/script.js"></script>
<script src="/js/token-handler.js"></script>
<script src="/phone-calls.js"></script>

<!-- Pass data from server to client using data attributes -->
<div id="server-data" 
     data-has-candidate="<%= typeof candidateData !== 'undefined' && candidateData ? 'true' : 'false' %>"
     data-has-token="<%= typeof completionToken !== 'undefined' && completionToken ? 'true' : 'false' %>"
     data-token="<%= typeof completionToken !== 'undefined' && completionToken ? completionToken : '' %>"
     data-preselected-department="<%= preselectedDepartment || '' %>"
     data-preselected-position="<%= preselectedPosition || '' %>"
     <% if (typeof candidateData !== 'undefined' && candidateData) { %>
     data-candidate="<%= encodeURIComponent(JSON.stringify(candidateData)) %>"
     <% } %>
     style="display: none;">
</div>

<script>
// Initialize variables
var candidateData = null;
var completionToken = null;

// Extract data from the server-data element
(function() {
    const serverData = document.getElementById('server-data');
    if (!serverData) return;
    
    // Get token if available
    if (serverData.getAttribute('data-has-token') === 'true') {
        completionToken = serverData.getAttribute('data-token');
        console.log('Completion token:', completionToken);
    }
    
    // Get candidate data if available
    if (serverData.getAttribute('data-has-candidate') === 'true') {
        try {
            // Parse the encoded candidate data
            const encodedData = serverData.getAttribute('data-candidate');
            if (encodedData) {
                candidateData = JSON.parse(decodeURIComponent(encodedData));
                console.log('Token-based candidate data loaded:', candidateData);
                
                // Manually call the prefill function with a delay to ensure DOM is ready
                setTimeout(function() {
                    console.log('Attempting to prefill form with data:', candidateData);
                    if (typeof prefillFormWithCandidateData === 'function') {
                        prefillFormWithCandidateData(candidateData);
                        console.log('Form prefill function called');
                    } else {
                        console.error('prefillFormWithCandidateData function not available!');
                    }
                }, 1000);
            }
        } catch (error) {
            console.error('Error parsing candidate data:', error);
        }
    }
})();

// Add an additional check after DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');
    if (candidateData) {
        console.log('Attempting secondary form prefill on DOMContentLoaded');
        setTimeout(function() {
            if (typeof prefillFormWithCandidateData === 'function') {
                prefillFormWithCandidateData(candidateData);
                console.log('Secondary form prefill completed');
            }
        }, 1500);
    }
    
    // Add a debug button to manually trigger form filling
    const debugBtn = document.createElement('button');
    debugBtn.textContent = 'Manual Fill';
    debugBtn.style.cssText = 'position:fixed; bottom:10px; right:10px; z-index:9999; background:#ff5722; color:white; padding:10px; border:none; border-radius:5px;';
    debugBtn.onclick = function() {
        console.log('Manual fill button clicked');
        if (candidateData) {
            prefillFormWithCandidateData(candidateData);
            console.log('Manual form fill completed');
        } else {
            console.error('No candidate data available for manual fill');
        }
    };
    document.body.appendChild(debugBtn);
});

document.addEventListener('DOMContentLoaded', async function() {
    const departmentSelect = document.getElementById('department');
    const positionSelect = document.getElementById('position');
    
    // Handle preselected values
    const serverData = document.getElementById('server-data');
    const preselectedDepartment = serverData.getAttribute('data-preselected-department');
    const preselectedPosition = serverData.getAttribute('data-preselected-position');
    
    if (preselectedDepartment) {
        // Add null check before using Array.from
        const options = departmentSelect.options || [];
        const departmentOption = Array.from(options).find(
            option => option && option.text && option.text.trim() === preselectedDepartment.trim()
        );
        
        if (departmentOption) {
            departmentOption.selected = true;
            // Trigger position update
            const event = new Event('change');
            departmentSelect.dispatchEvent(event);
        }
    }

    // Initialize validation icons
    function updateValidationIcon(element) {
        const icon = element.nextElementSibling;
        if (element.value) {
            element.classList.add('is-valid');
            element.classList.remove('is-invalid');
        } else {
            element.classList.add('is-invalid');
            element.classList.remove('is-valid');
        }
    }

    // Add validation to department select
    departmentSelect.addEventListener('change', function() {
        updateValidationIcon(this);
    });

    // Add validation to position select
    positionSelect.addEventListener('change', function() {
        updateValidationIcon(this);
    });

    // Initial validation check
    updateValidationIcon(departmentSelect);
    updateValidationIcon(positionSelect);
});
</script>

<!-- Updated Notification Bar -->
<div id="notificationBar" class="notification-bar" style="display: none;">
    <div class="notification-content">
        <i class="notification-icon fas"></i>
        <span id="notificationMessage"></span>
    </div>
    <span id="closeNotification" class="close-notification">
        <i class="fas fa-times"></i>
    </span>
</div>

<style>
.notification-bar {
  position: fixed;
  top: 135px;
  left: 310px;
  width: 50%;
  background-color: #d4edda;
  color: #155724;
  padding: 13px;
  text-align: center;
  border: 1px solid #c3e6cb;
  border-radius: 5px;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.close-notification {
  margin-left: 20px;
  cursor: pointer;
  font-weight: bold;
}

.new-button {
    background: #28a745;
    margin-left: 10px;
}

.new-button:hover {
    background: #218838;
}

.submit-container {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.submitting {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Styles for readonly position info */
.readonly-position-info {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.info-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px dashed #e9ecef;
}

.info-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.info-label {
    font-weight: bold;
    color: #495057;
    min-width: 120px;
}

.info-label i {
    margin-right: 8px;
    color: #008D5C;
}

.info-value {
    flex: 1;
    color: #212529;
    font-size: 1.05em;
}

/* Style for Expected Salary when accessed via token */
.position-section .salary-container {
    margin-top: 15px;
}
</style>

<!-- Add this before closing body tag -->
<script src="/js/position-handler.js"></script>

<!-- Add position selector scripts -->
<script src="/position-selector.js"></script>
<script src="/auto-fill.js"></script>

<!-- Mobile Zoom Adaptation Script -->
<script>
// Function to handle mobile zoom adaptation
function handleMobileZoom() {
  // Only run on mobile devices
  if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
    // Set viewport meta tag dynamically
    const viewportMeta = document.querySelector('meta[name="viewport"]');
    if (viewportMeta) {
      // Allow zoom for better readability
      viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0, viewport-fit=cover');
    }
    
    // Add mobile-specific styles
    const mobileStyles = document.createElement('style');
    mobileStyles.textContent = `
      @media screen and (max-width: 767px) {
        input, select, textarea, button {
          font-size: 16px !important; /* Prevents zoom on focus in iOS */
        }
        
        .select2-container .select2-selection--single {
          height: 48px !important;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__rendered {
          line-height: 48px !important;
        }
        
        body {
          -webkit-text-size-adjust: 100%;
        }
        
        /* Improve touch targets */
        .button, .input-container input, .input-container select {
          min-height: 48px;
          padding: 12px 15px;
        }
      }
    `;
    document.head.appendChild(mobileStyles);
    
    // Scale content to fit screen better on load and orientation change
    function adjustScale() {
      // Force reflow to adjust content
      document.body.style.display = 'none';
      document.body.offsetHeight; // Force reflow
      document.body.style.display = '';
      
      // Scroll to top for better visibility
      window.scrollTo(0, 0);
    }
    
    // Apply on page load and orientation change
    window.addEventListener('load', adjustScale);
    window.addEventListener('orientationchange', adjustScale);
    
    // Fix input zooming issues
    document.addEventListener('focus', function(e) {
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT' || e.target.tagName === 'TEXTAREA') {
        // Add some padding to ensure element is visible
        setTimeout(function() {
          document.body.scrollTop = document.body.scrollTop + 1;
          document.body.scrollTop = document.body.scrollTop - 1;
        }, 300);
      }
    }, true);
  }
}

// Run on DOM content loaded
document.addEventListener('DOMContentLoaded', handleMobileZoom);
</script>

<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="script.js"></script>
<script src="js/phone-call-handler.js"></script>

<%- include('phone-call-modal.ejs') %>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fix for iOS touch events
        const submitButton = document.getElementById('submitBtn');
        if (submitButton) {
            // Use multiple event listeners to ensure it works on all devices
            submitButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Submit button clicked');
                submitForm();
            });
            
            submitButton.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Submit button touched');
                submitForm();
            });
            
            // Make sure button is properly sized for touch
            submitButton.style.minHeight = '44px';
            submitButton.style.padding = '12px 25px';
        }
    });
</script>
</body>
</html>
