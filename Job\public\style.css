@import url('https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,400;0,700;1,400;1,700&family=Roboto+Slab:wght@400;700&display=swap');

html {
  height: 100%;
  min-height:800px;
}
body {
  background: url('./images/19873.jpg');
  background-size:cover;
  background-repeat:no-repeat;
  text-align: center;
 font-family: 'Noto Sans', sans-serif;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  min-width: 800px;
  overflow-x: auto;
  -webkit-text-size-adjust: none;
}

h1{
  font-weight:400;
  padding: 20px 0;
  margin: 0 0 20px 0;
  font-family: 'Roboto Slab', serif;
}

#svg_form_time {
  height: 15px;
  width: 80%;
  margin: 20px auto 20px;
  display: block;
}

#svg_form_time circle,
#svg_form_time rect {
  fill: #008D5C;
  stroke: white;
  stroke-width: 1;
}

.button {
  background: #008D5C;
  border-radius: 5px;
  padding: 15px 25px;
  display: inline-block;
  margin: 10px;
  font-weight: bold;
  color: white;
  cursor: pointer;
  box-shadow:0px 2px 5px rgb(0,0,0,0.5);
}

.disabled {
  display:none;
}

section {
  padding: 30px;
  width: 80%;
  margin: 0 auto 30px;
  background: rgba(255,255,255,0.9);
  box-shadow: 0px 2px 10px rgba(0,0,0,0.3);
  border-radius: 5px;
  transform: none;
}


input, select {
  width: 100%;
  margin: 7px 0px;
  display: inline-block;
  padding: 12px 25px;
  box-sizing: border-box;
  border-radius: 5px;
  border: 1px solid lightgrey;
  font-size: 1em;
  font-family:inherit;
  background:white;
}

p{
  text-align:justify;
margin-top:0;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background: url('/images/user-cover.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.3);
  border-radius: 5px;
  margin-bottom: 20px;
  position: relative;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
}

.logo, .header-title {
  position: relative;
  z-index: 1;
}

.header-title {
  font-family: 'Roboto Slab', serif;
  font-size: 1.5em;
  font-weight: 700;
  color: white;
}

.logo {
  height: 50px;
  margin-right: 20px;
}

/* Add these styles for validation icons */
.input-container {
    position: relative;
    margin-bottom: 20px;
}

.input-container input,
.input-container select {
    width: 100%;
    padding: 12px 45px 12px 40px;
    font-weight: 600;
    color: #333;
}

.validation-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    display: none;
}

.input-container input.is-valid + .validation-icon,
.input-container select.is-valid + .validation-icon {
    display: block;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2328a745"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/></svg>') no-repeat center center;
}

.input-container input.is-invalid + .validation-icon,
.input-container select.is-invalid + .validation-icon {
    display: block;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23dc3545"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/></svg>') no-repeat center center;
}

.error-message {
    color: #dc3545;
    font-size: 0.8em;
    margin-top: 5px;
    display: none;
    padding-left: 10px;
}

.input-container input.is-invalid ~ .error-message,
.input-container select.is-invalid ~ .error-message {
    display: block;
}

@viewport {
  width: device-width;
  zoom: 0.8;
  min-zoom: 0.8;
  max-zoom: 0.8;
  user-zoom: fixed;
}

/* Update select styling */
.input-container select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
}

.input-container select:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

.input-container select option {
    padding: 8px;
}

/* Style for select container */
.input-container.city,
.input-container.marital,
.input-container.military {
    position: relative;
    margin-bottom: 15px;
}

/* Style for select when focused */
.input-container select:focus {
    outline: none;
    border-color: #008D5C;
    box-shadow: 0 0 0 2px rgba(0, 141, 92, 0.1);
}

/* Style for select placeholder */
.input-container select option[value=""][disabled] {
    color: #666;
}

/* Style for select options */
.input-container select option {
    color: #333;
    padding: 10px;
}

/* Add hover effect */
.input-container select:hover {
    border-color: #008D5C;
}

/* Ensure icons are properly positioned for select elements */
.input-container.city::before,
.input-container.marital::before,
.input-container.military::before {
    z-index: 2;
}

/* Add specific colors for validation states */
.input-container select.is-valid {
    border-color: #28a745;
}

.input-container select.is-invalid {
    border-color: #dc3545;
}

/* Add icon styling */
.input-container::before {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #008D5C;
    z-index: 1;
}

/* Add icons for specific fields */
.input-container.name::before { content: "\f007"; } /* user icon */
.input-container.phone::before { content: "\f095"; } /* phone icon */
.input-container.id::before { content: "\f2c2"; } /* id card icon */
.input-container.age::before { content: "\f1fd"; } /* birthday cake icon */
.input-container.birth::before { content: "\f073"; } /* calendar icon */
.input-container.location::before { content: "\f3c5"; } /* map marker icon */
.input-container.gender::before { content: "\f228"; } /* venus-mars icon */
.input-container.address::before { content: "\f015"; } /* home icon */
.input-container.city::before { content: "\f64f"; } /* city icon */
.input-container.marital::before { content: "\f004"; } /* heart icon */
.input-container.military::before { content: "\f132"; } /* shield icon */
.input-container.email::before { content: "\f0e0"; } /* envelope icon */

/* Style for readonly fields */
.input-container input[readonly] {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

/* Make placeholder text slightly darker */
::placeholder {
    color: #666;
    font-weight: normal;
}

/* Add icons for educational fields */
.input-container.degree::before { content: "\f5a2"; } /* graduation cap icon */
.input-container.specialization::before { content: "\f02d"; } /* book icon */
.input-container.university::before { content: "\f19c"; } /* university icon */
.input-container.graduation::before { content: "\f133"; } /* calendar icon */

/* Style for educational select */
.input-container.degree select {
    width: 100%;
    padding: 12px 45px 12px 40px;
    font-weight: 600;
    color: #333;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23008D5C' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 15px;
}

/* Add icons for experience fields */
.input-container.employer::before { content: "\f1ad"; } /* building icon */
.input-container.job::before { content: "\f2c1"; } /* id badge icon */
.input-container.date::before { content: "\f073"; } /* calendar icon */
.input-container.salary::before { content: "\f53a"; } /* money bill wave icon */
.input-container.reason::before { content: "\f075"; } /* comment icon */
.input-container.source::before { content: "\f0a1"; } /* bullhorn icon */

/* Style for date inputs */
.input-container input[type="date"] {
    width: 100%;
    padding: 12px 45px 12px 40px;
    font-weight: 600;
    color: #333;
}

/* Style for date inputs in webkit browsers */
.input-container input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    right: 15px;
    background: transparent;
    color: #008D5C;
    cursor: pointer;
}

/* Style for source select */
.input-container.source select {
    width: 100%;
    padding: 12px 45px 12px 40px;
    font-weight: 600;
    color: #333;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23008D5C' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 15px;
}

/* Add icons for position fields */
.input-container.department::before { content: "\f0e8"; } /* sitemap icon */
.input-container.position::before { content: "\f0ae"; } /* tasks icon */

/* Style for disabled select */
.input-container select:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

/* File upload styling */
.custom-file-wrapper {
    position: relative;
    width: 100%;
    margin-bottom: 1rem;
}

.custom-file-input {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 2;
}

.custom-file-label {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: all 0.2s ease-in-out;
}

.custom-file-label::before {
    content: '📎';
    margin-right: 10px;
    font-size: 1.2em;
}

.custom-file-label.file-selected {
    background-color: #e8f5e9;
    border-color: #008D5C;
    color: #008D5C;
}

.custom-file-wrapper:hover .custom-file-label {
    border-color: #008D5C;
}

/* Add loading state styles */
.custom-file-label.loading {
    background-color: #f8f9fa;
    color: #6c757d;
}

.custom-file-label.loading::after {
    content: '';
    display: inline-block;
    width: 1rem;
    height: 1rem;
    margin-left: 0.5rem;
    border: 2px solid #6c757d;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Salary input icon */
.input-container.salary::before {
    content: '\f53a';
}

/* Add icons for other information fields */
.input-container.language::before { content: "\f1ab"; } /* language icon */
.input-container.computer::before { content: "\f108"; } /* desktop icon */
.input-container.hospital::before { content: "\f0f8"; } /* hospital icon */
.input-container.health::before { content: "\f21e"; } /* heartbeat icon */
.input-container.relatives::before { content: "\f0c0"; } /* users icon */

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 500px;
    border-radius: 5px;
    text-align: center;
}

.loader {
    text-align: center;
    padding: 20px;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-radius: 50%;
    border-top: 4px solid #008D5C;
    width: 40px;
    height: 40px;
    margin: 0 auto 15px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Language Toggle Button */
.toggle {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 60px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 4px;
  cursor: pointer;
  z-index: 100;
}

.toggle img {
  position: absolute;
  top: 4px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
  transition: all 0.2s ease-in-out;
}

.toggle img.en {
  left: 4px;
}

.toggle img.ar {
  left: 34px;
  transform: rotate(360deg);
}

.noselect {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Add these styles for the finish section */
.finish-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.review-section {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.review-section h3 {
    color: #008D5C;
    margin-bottom: 20px;
    font-size: 1.5em;
    text-align: center;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.summary-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #008D5C;
}

.summary-item.rtl {
    border-left: none;
    border-right: 4px solid #008D5C;
}

.summary-label {
    font-weight: 600;
    color: #666;
    margin-bottom: 5px;
    font-size: 0.9em;
}

.summary-value {
    color: #333;
    font-size: 1.1em;
}

.submit-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: 30px;
}

.loader {
    display: flex;
    align-items: center;
    gap: 10px;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #008D5C;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

.submit-button, .new-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 25px;
    font-size: 1.1em;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-button {
    background: #008D5C;
    color: white;
}

.submit-button:hover {
    background: #006B44;
    transform: translateY(-1px);
}

.new-button {
    background: #28a745;
    color: white;
}

.new-button:hover {
    background: #218838;
    transform: translateY(-1px);
}

/* RTL Support */
[dir="rtl"] .summary-grid {
    text-align: right;
}

[dir="rtl"] .submit-button i,
[dir="rtl"] .new-button i {
    margin-left: 8px;
    margin-right: 0;
}

/* RTL Support */
.rtl-text {
    text-align: right;
    direction: rtl;
}

/* RTL Input styles */
[dir="rtl"] input,
[dir="rtl"] select,
[dir="rtl"] textarea {
    text-align: right;
}

[dir="rtl"] .input-container::before {
    left: auto;
    right: 15px;
}

[dir="rtl"] .validation-icon {
    right: auto;
    left: 10px;
}

[dir="rtl"] .error-message {
    text-align: right;
    padding-right: 10px;
    padding-left: 0;
}

/* RTL Select styles */
[dir="rtl"] select {
    padding: 12px 40px 12px 45px;
    background-position: left 15px center;
}

/* RTL Checkbox styles */
[dir="rtl"] .checkbox-container {
    padding-left: 0;
    padding-right: 35px;
}

[dir="rtl"] .checkmark {
    left: auto;
    right: 0;
}

/* RTL Button styles */
[dir="rtl"] .button {
    font-family: 'Noto Sans Arabic', sans-serif;
}

[dir="rtl"] .new-button {
    margin-left: 0;
    margin-right: 10px;
}

/* RTL Icons */
[dir="rtl"] .fas {
    margin-right: 0;
    margin-left: 8px;
}

/* RTL Notification */
[dir="rtl"] .notification-bar {
    right: auto;
    left: 20px;
    border-left: none;
    border-right: 4px solid;
}

[dir="rtl"] .close-notification {
    margin-left: 0;
    margin-right: 10px;
}

/* RTL Language Toggle */
[dir="rtl"] .toggle {
    left: 20px;
    right: auto;
}

/* RTL Form Layout */
[dir="rtl"] .finish-content,
[dir="rtl"] #applicationSummary,
[dir="rtl"] .terms-text {
    text-align: right;
}

[dir="rtl"] .terms-text ul {
    padding-right: 20px;
    padding-left: 0;
}

[dir="rtl"] .terms-text li:before {
    right: -20px;
    left: auto;
}

/* RTL support */
body.rtl {
    direction: rtl;
    text-align: center;
}

body.rtl .finish-content {
    text-align: right;
}

body.rtl .terms-text ul {
    padding-right: 20px;
    padding-left: 0;
}

body.rtl .submit-button {
    font-family: 'Noto Sans Arabic', sans-serif;
}

/* Ensure icons stay on the correct side */
body.rtl .fa, 
body.rtl .fas {
    margin-left: 5px;
    margin-right: 0;
}

/* Updated Notification Styles */
.notification-bar {
    position: fixed;
    top: 20px;
    right: 20px;
    width: auto;
    min-width: 300px;
    max-width: 400px;
    padding: 15px 20px;
    border-radius: 8px;
    display: none; /* Hide by default */
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    transform: translateX(120%);
    transition: all 0.3s ease-in-out;
}

.notification-bar.show {
    display: flex; /* Show when .show class is added */
    opacity: 1;
    transform: translateX(0);
}

.notification-bar.success {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
    color: #155724;
}

.notification-bar.error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.notification-bar.warning {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    color: #856404;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-icon {
    font-size: 20px;
}

.notification-message {
    font-size: 14px;
    line-height: 1.4;
}

.close-notification {
    cursor: pointer;
    padding: 4px;
    margin-left: 10px;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.close-notification:hover {
    opacity: 1;
}

/* RTL Support for notifications */
[dir="rtl"] .notification-bar {
    right: auto;
    left: 20px;
    border-left: none;
    border-right: 4px solid;
}

[dir="rtl"] .close-notification {
    margin-left: 0;
    margin-right: 10px;
}

/* Add disabled button styles */
.button.disabled {
    background: #cccccc;
    cursor: not-allowed;
    opacity: 0.7;
    display: inline-block; /* Override display:none */
    pointer-events: auto; /* Allow click events for showing message */
}

.button.disabled:hover {
    background: #cccccc;
    box-shadow: 0px 2px 5px rgb(0,0,0,0.5);
}

/* Position Section Styles */
.position-section {
    padding: 20px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.position-container {
    margin: 20px 0;
}

.search-wrapper {
    position: relative;
    margin-bottom: 15px;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
    z-index: 2;
}

.search-clear {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
    cursor: pointer;
    display: none;
    z-index: 2;
}

.search-clear:hover {
    color: #dc3545;
}

.position-search:not(:placeholder-shown) + .search-clear {
    display: block;
}

.position-search {
    width: 100%;
    padding: 12px 40px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.position-search:focus {
    border-color: #008D5C;
    box-shadow: 0 0 0 3px rgba(0, 141, 92, 0.1);
    outline: none;
    background-color: #fff;
}

.search-status {
    font-size: 0.9em;
    color: #666;
    margin-top: 5px;
    text-align: right;
}

.positions-list {
    max-height: 300px;
    overflow-y: auto;
    margin: 15px 0;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fff;
}

.position-item {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: all 0.2s ease;
}

.position-item:last-child {
    border-bottom: none;
}

.position-item:hover {
    background-color: #f8f9fa;
}

.position-item.selected {
    background-color: #e8f5e9;
    border-left: 4px solid #008D5C;
}

.position-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.position-details {
    font-size: 0.9em;
    color: #666;
}

.selected-position-details {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.form-group {
    margin-bottom: 15px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #666;
    font-size: 0.9em;
}

.form-group label i {
    margin-right: 8px;
    color: #008D5C;
}

.form-group input[readonly] {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    padding: 10px 15px;
    border-radius: 6px;
    width: 100%;
    color: #2c3e50;
    font-size: 0.95em;
}

.salary-container {
    margin-top: 20px;
}

.input-container.salary {
    position: relative;
}

.input-container.salary label {
    display: block;
    margin-bottom: 8px;
    color: #666;
    font-size: 0.9em;
}

.input-container.salary label i {
    margin-right: 8px;
    color: #008D5C;
}

.input-container.salary input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.input-container.salary input:focus {
    border-color: #008D5C;
    box-shadow: 0 0 0 3px rgba(0, 141, 92, 0.1);
    outline: none;
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #666;
}

/* RTL Support */
[dir="rtl"] .search-icon {
    left: auto;
    right: 15px;
}

[dir="rtl"] .search-clear {
    right: auto;
    left: 15px;
}

[dir="rtl"] .position-search {
    padding: 12px 40px;
    text-align: right;
}

[dir="rtl"] .search-status {
    text-align: left;
}

[dir="rtl"] .position-item.selected {
    border-left: none;
    border-right: 4px solid #008D5C;
}

[dir="rtl"] .form-group label i,
[dir="rtl"] .input-container.salary label i {
    margin-right: 0;
    margin-left: 8px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .position-section {
        padding: 15px;
    }

    .positions-list {
        max-height: 250px;
    }

    .position-search,
    .input-container.salary input {
        font-size: 14px;
        padding: 10px 35px;
    }

    .position-title {
        font-size: 0.95em;
    }

    .position-details {
        font-size: 0.85em;
    }
}

/* Highlight matches */
.highlight {
    background-color: rgba(0, 141, 92, 0.1);
    color: #008D5C;
    padding: 0 2px;
    border-radius: 2px;
}

.no-results {
    padding: 30px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.no-results i {
    font-size: 24px;
    color: #008D5C;
    margin-bottom: 10px;
    display: block;
}

/* Position item icons */
.position-details i {
    color: #008D5C;
    margin-right: 5px;
    font-size: 0.9em;
}

[dir="rtl"] .position-details i {
    margin-right: 0;
    margin-left: 5px;
}

/* Add spacing between department and section */
.position-details i.ml-2 {
    margin-left: 10px;
}

[dir="rtl"] .position-details i.ml-2 {
    margin-left: 0;
    margin-right: 10px;
}

/* Mobile-specific styles */
@media (max-width: 768px) {
    body.mobile-device {
        font-size: 16px; /* Slightly larger base font for readability */
    }
    
    body.mobile-device .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    /* Larger form inputs for touch targets */
    body.mobile-device .form-control {
        height: 44px;
        font-size: 16px; /* Prevents iOS zoom on focus */
    }
    
    /* Fix for iOS date inputs */
    body.mobile-device input[type="date"] {
        appearance: none;
        -webkit-appearance: none;
        min-height: 44px;
        text-indent: 5px;
    }
    
    /* Larger select2 inputs for mobile */
    body.mobile-device .select2-container .select2-selection--single {
        height: 44px;
    }
    
    body.mobile-device .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 44px;
        font-size: 16px;
    }
    
    body.mobile-device .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 42px;
    }
    
    /* Larger buttons for touch targets */
    body.mobile-device .btn {
        padding: 12px 20px;
        font-size: 16px;
        min-height: 44px;
    }
    
    /* Fix layout spacing issues on small screens */
    body.mobile-device .row {
        margin-left: -5px;
        margin-right: -5px;
    }
    
    body.mobile-device .col-12,
    body.mobile-device .col-md-6,
    body.mobile-device .col-lg-4 {
        padding-left: 5px;
        padding-right: 5px;
    }
    
    /* Make sure error messages are visible */
    body.mobile-device .invalid-feedback {
        display: block;
        font-size: 14px;
    }
    
    /* Improve SweetAlert modals on mobile */
    body.mobile-device .swal2-popup {
        font-size: 16px;
        width: 90%;
    }
    
    /* Adjust form actions margin */
    body.mobile-device .form-actions {
        margin-top: 20px;
        padding-bottom: 30px; /* More space at bottom for iOS */
    }
}

/* iOS-specific fixes */
@supports (-webkit-touch-callout: none) {
    /* Fix for iOS form elements */
    input, select, textarea {
        font-size: 16px !important; /* Prevents iOS zoom on focus */
    }
    
    /* Fix for iOS scrolling */
    body {
        -webkit-overflow-scrolling: touch;
    }
}

/* Mobile device orientation changes */
@media (max-width: 576px) and (orientation: landscape) {
    body.mobile-device .container {
        max-width: 95%;
    }
    
    body.mobile-device .form-group {
        margin-bottom: 0.5rem;
    }
}

/* Add a utility class to fix iOS date inputs */
.ios-date-input-fix {
    position: relative;
}

.ios-date-input-fix:after {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

/* Fix for connection issues overlay */
.offline-indicator {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffcc00;
    color: #333;
    text-align: center;
    padding: 10px;
    font-weight: bold;
    z-index: 9999;
}

body.is-offline .offline-indicator {
    display: block;
}

/* Add specific fixes for the submit button on mobile devices */
#submitBtn {
    position: relative;
    z-index: 100; /* Ensure button is on top of other elements */
    -webkit-appearance: none; /* Remove default styling on iOS */
    -webkit-tap-highlight-color: rgba(0,0,0,0); /* Remove tap highlight on iOS */
    touch-action: manipulation; /* Improve touch handling */
    cursor: pointer;
    user-select: none;
    outline: none !important;
    /* More visible button design */
    font-weight: bold;
    font-size: 16px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transform: translateZ(0); /* Force hardware acceleration */
    transition: background-color 0.3s ease;
}

/* Active state styles */
#submitBtn:active {
    transform: translateY(2px) translateZ(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    background-color: #006B44; /* Darker green when pressed */
}

/* Add a larger touch target for mobile */
@media (max-width: 768px) {
    #submitBtn {
        min-height: 50px;
        min-width: 200px;
        padding: 14px 28px;
        font-size: 18px;
        margin: 20px auto;
    }
    
    /* Add extra padding around the button for easier tapping */
    .submit-container {
        padding: 15px;
        position: relative;
        z-index: 100;
    }
    
    /* Remove other elements that might interfere with button clicks */
    .submit-container::before, 
    .submit-container::after {
        display: none;
    }
}

/* Safari-specific fix */
@supports (-webkit-touch-callout: none) {
    #submitBtn {
        cursor: pointer;
        -webkit-touch-callout: none;
    }
}

