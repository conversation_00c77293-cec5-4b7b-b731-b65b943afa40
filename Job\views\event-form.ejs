<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Application Form - Shifa Hospitals</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.0.18/dist/sweetalert2.min.css">
    <link rel="icon" href="Images/favicon.png" type="image/png">
    <style>
        :root {
            --primary-color: #2c5282;
            --secondary-color: #4299e1;
            --accent-color: #90cdf4;
            --text-color: #2d3748;
            --light-bg: #f7fafc;
        }
        
        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
        }
        
        .form-container {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            padding: 35px;
            margin-top: 40px;
            margin-bottom: 40px;
            transition: all 0.3s ease;
        }
        
        .form-container:hover {
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 35px;
        }
        
        .form-header img {
            max-width: 180px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .form-header img:hover {
            transform: scale(1.05);
        }
        
        .form-header h2 {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .form-header p {
            color: #718096;
            font-size: 1.1rem;
        }
        
        .form-group label {
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 8px;
        }
        
        .form-control {
            height: calc(2.5em + 0.75rem + 2px);
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
            padding-left: 15px;
        }
        
        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
            border-color: var(--secondary-color);
        }
        
        .select2-container--default .select2-selection--single {
            height: calc(2.5em + 0.75rem + 2px);
            padding: 0.375rem 0.75rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: calc(2.5em + 0.75rem + 2px);
        }
        
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 2em;
            color: var(--text-color);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            padding: 12px 24px;
            font-weight: 600;
            border-radius: 8px;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background-color: #1a365d;
            border-color: #1a365d;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(44, 82, 130, 0.3);
        }
        
        .required-field::after {
            content: "*";
            color: #e53e3e;
            margin-left: 4px;
        }
        
        .invalid-feedback {
            display: none;
            color: #e53e3e;
            font-size: 0.875rem;
            margin-top: 6px;
        }
        
        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
            display: none;
            vertical-align: text-bottom;
            margin-right: 8px;
            border-width: 0.2em;
        }
        
        .is-loading .spinner-border {
            display: inline-block;
        }
        
        .custom-file-label {
            height: calc(2.5em + 0.75rem + 2px);
            line-height: 2;
            border-radius: 8px;
            padding-left: 15px;
            border: 1px solid #e2e8f0;
        }
        
        .custom-file-label::after {
            height: calc(2.5em + 0.75rem);
            line-height: 2;
            background-color: #edf2f7;
            color: var(--text-color);
            border-radius: 0 8px 8px 0;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .row {
            margin-bottom: 0.5rem;
        }
        
        /* Animated validation icons */
        .is-valid {
            border-color: #48bb78 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2348bb78' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        .is-invalid {
            border-color: #e53e3e !important;
        }
        
        /* Shake animation for invalid fields */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        .shake {
            animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
        }
        
        /* Tooltip styles */
        .tooltip-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background-color: var(--secondary-color);
            color: white;
            font-size: 12px;
            margin-left: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .tooltip-icon:hover {
            background-color: var(--primary-color);
            transform: scale(1.1);
        }
        
        /* Larger hitboxes for interactive elements */
        .select2-container .select2-selection--single {
            height: 48px !important;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 48px !important;
            padding-left: 15px;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 48px !important;
            width: 30px !important;
        }
        
        .custom-file {
            height: 48px;
        }
        
        .custom-file-input {
            height: 48px;
            cursor: pointer;
        }
        
        .custom-file-label {
            height: 48px !important;
            padding: 0.75rem 1rem;
            line-height: 1.5 !important;
            cursor: pointer;
        }
        
        .custom-file-label::after {
            height: 46px !important;
            padding: 0.75rem 1rem;
            line-height: 1.5 !important;
        }
        
        /* Pulse animation for empty required fields on submit */
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(229, 62, 62, 0.5); }
            70% { box-shadow: 0 0 0 10px rgba(229, 62, 62, 0); }
            100% { box-shadow: 0 0 0 0 rgba(229, 62, 62, 0); }
        }
        
        .pulse {
            animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        /* Helper text */
        .helper-text {
            display: block;
            font-size: 0.8rem;
            color: #718096;
            margin-top: 0.25rem;
        }
        
        /* Custom tooltip */
        .custom-tooltip {
            position: relative;
            display: inline-block;
        }
        
        .custom-tooltip .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: #2d3748;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.75rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .custom-tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #2d3748 transparent transparent transparent;
        }
        
        .custom-tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        
        @media (max-width: 768px) {
            .form-container {
                padding: 25px 20px;
                margin-top: 20px;
                margin-bottom: 20px;
            }
            
            .form-header img {
                max-width: 150px;
            }
            
            .form-header h2 {
                font-size: 1.5rem;
            }
            
            .btn-primary {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10 col-lg-8">
                <div class="form-container">
                    <div class="form-header">
                        <img src="Images/logo.png" alt="Shifa Hospitals Logo">
                        <h2>Event Application Form</h2>
                        <p class="text-muted">Please fill out the form below to apply</p>
                    </div>

                    <form id="eventApplicationForm" enctype="multipart/form-data">
                        <!-- Hidden fields for tracking -->
                        <input type="hidden" name="isEvent" value="true">
                        <input type="hidden" name="submissionSource" id="submissionSource" value="<%= eventName %>">
                        <input type="hidden" name="Application_Status" value="Event">
                        
                        <!-- Hidden fields for position hierarchy -->
                        <input type="hidden" name="Main_Position" id="departmentId" value="1">
                        <input type="hidden" name="SECTIONId" id="sectionId" value="1">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="firstName" class="required-field">First Name</label>
                                    <input type="text" class="form-control" id="firstName" name="firstName" required>
                                    <div class="invalid-feedback">Please enter your first name</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="lastName" class="required-field">Last Name</label>
                                    <input type="text" class="form-control" id="lastName" name="lastName" required>
                                    <div class="invalid-feedback">Please enter your last name</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone" class="required-field">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="Mobile" pattern="[0-9]{11}" required>
                                    <div class="invalid-feedback">Please enter a valid 11-digit phone number</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email">Email (Optional)</label>
                                    <input type="email" class="form-control" id="email" name="Email">
                                    <div class="invalid-feedback">Please enter a valid email address</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="idType" class="required-field">ID Type</label>
                                    <select class="form-control" id="idType" name="ID_Type" required>
                                        <option value="national">National ID</option>
                                        <option value="passport">Passport</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="nationalId" id="idLabel" class="required-field">National ID
                                        <span class="custom-tooltip">
                                            <span class="tooltip-icon">?</span>
                                            <span class="tooltip-text" id="idTooltip">Egyptian National ID should be 14 digits. Passport numbers vary by country.</span>
                                        </span>
                                    </label>
                                    <input type="text" class="form-control" id="nationalId" name="National_ID" required>
                                    <div class="invalid-feedback" id="idFeedback">Please enter a valid 14-digit National ID</div>
                                    <small class="helper-text" id="idHelper">For Egyptian citizens: 14 digits without spaces</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="gender">Gender (Optional)</label>
                                    <select class="form-control" id="gender" name="Gender">
                                        <option value="">Select Gender</option>
                                        <option value="Male">Male</option>
                                        <option value="Female">Female</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="position" class="required-field">Position Interested In</label>
                                    <select class="form-control" id="position" name="Sub_Position" required>
                                        <option value="">Select Position</option>
                                        <!-- Positions will be loaded dynamically -->
                                    </select>
                                    <div class="invalid-feedback">Please select a position</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="cvFile">Upload CV (Optional)</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="cvFile" name="cvFile" accept=".pdf,.doc,.docx">
                                <label class="custom-file-label" for="cvFile">Choose file</label>
                            </div>
                            <small class="form-text text-muted">Accepted formats: PDF, DOC, DOCX (Max 5MB)</small>
                        </div>

                        <div class="form-group text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                <span class="spinner-border" role="status" aria-hidden="true"></span>
                                Submit Application
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.0.18/dist/sweetalert2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for dropdowns
            $('#position').select2({
                placeholder: 'Select Position',
                theme: 'bootstrap4'
            });
            
            $('#gender').select2({
                placeholder: 'Select Gender',
                theme: 'bootstrap4',
                minimumResultsForSearch: Infinity
            });
            
            $('#idType').select2({
                placeholder: 'Select ID Type',
                theme: 'bootstrap4',
                minimumResultsForSearch: Infinity
            });
            
            // Load positions
            loadPositions();
            
            // Handle ID type change
            $('#idType').change(function() {
                toggleIdFields($(this).val() === 'passport');
            });
            
            // Handle file input display
            $('.custom-file-input').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName ? fileName : 'Choose file');
            });
            
            // Real-time validation
            $('.form-control').on('blur', function() {
                validateField($(this));
            });
            
            // Form submission
            $('#eventApplicationForm').on('submit', function(e) {
                e.preventDefault();
                
                if (!validateForm()) {
                    // Scroll to first error
                    $('html, body').animate({
                        scrollTop: $('.is-invalid:first').offset().top - 100
                    }, 500);
                    return false;
                }
                
                submitFormWithRetry(new FormData(this), 1, 3);
            });
            
            // Function to validate a single field
            function validateField(field) {
                const id = field.attr('id');
                const value = field.val().trim();
                
                field.removeClass('is-valid is-invalid shake pulse');
                
                // Skip optional empty fields
                if (!field.prop('required') && value === '') {
                    return true;
                }
                
                let isValid = true;
                
                switch(id) {
                    case 'firstName':
                    case 'lastName':
                        isValid = value !== '';
                        break;
                    case 'phone':
                        isValid = /^[0-9]{11}$/.test(value);
                        break;
                    case 'email':
                        if (value) {
                            isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
                            // If email is provided but invalid, show specific message
                            if (!isValid) {
                                field.siblings('.invalid-feedback').text('Please enter a valid email address format (e.g., <EMAIL>)');
                            }
                        }
                        break;
                    case 'nationalId':
                        const idType = $('#idType').val();
                        if (idType === 'national') {
                            // Stricter validation for National ID based on database constraints
                            isValid = /^\d{14}$/.test(value);
                        } else {
                            isValid = value.length >= 6;
                        }
                        break;
                    case 'position':
                        isValid = value !== '';
                        break;
                }
                
                if (isValid) {
                    field.addClass('is-valid');
                } else {
                    field.addClass('is-invalid');
                }
                
                return isValid;
            }
            
            // Function to toggle ID fields based on type
            function toggleIdFields(isPassport) {
                if (isPassport) {
                    $('#idLabel').text('Passport Number');
                    $('#nationalId').attr('pattern', '[A-Za-z0-9]{6,20}');
                    $('#idFeedback').text('Please enter a valid passport number');
                    $('#idTooltip').text('Passport numbers typically contain 6-20 alphanumeric characters.');
                    $('#idHelper').text('Enter your passport number with no spaces');
                    
                    // Change name attribute for passport - but we'll handle this in submission
                    $('#nationalId').attr('data-idtype', 'passport');
                } else {
                    $('#idLabel').text('National ID');
                    $('#nationalId').attr('pattern', '[0-9]{14}');
                    $('#idFeedback').text('Please enter a valid 14-digit National ID');
                    $('#idTooltip').text('Egyptian National ID should be 14 digits. Passport numbers vary by country.');
                    $('#idHelper').text('For Egyptian citizens: 14 digits without spaces');
                    
                    // Restore original name attribute
                    $('#nationalId').attr('data-idtype', 'national');
                }
                
                // Revalidate the field
                validateField($('#nationalId'));
            }
            
            // Function to load positions
            function loadPositions() {
                $.ajax({
                    url: '/api/positions',
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        const positionSelect = $('#position');
                        positionSelect.empty().append('<option value="">Select Position</option>');
                        
                        // Debug: Log the first position item to see its structure
                        if (data && data.length > 0) {
                            console.log('First position object structure:', JSON.stringify(data[0], null, 2));
                            console.log('Available fields:', Object.keys(data[0]).join(', '));
                        }
                        
                        // Store the full position data to access department and section info
                        window.positionsData = data;
                        
                        if (Array.isArray(data)) {
                            data.forEach(function(position, index) {
                                // Determine the correct ID field to use
                                // Try different possible ID fields
                                let posId = position.PositionId;
                                
                                // If PositionId is undefined, try HIMS_ID as a fallback
                                if (posId === undefined && position.HIMS_ID !== undefined) {
                                    posId = position.HIMS_ID;
                                    console.log(`Using HIMS_ID (${posId}) for position ${position.PositionName}`);
                                }
                                
                                // If both are undefined, use the array index + 1000 as a temporary ID
                                if (posId === undefined) {
                                    posId = index + 1000;
                                    console.log(`Generated temporary ID (${posId}) for position ${position.PositionName}`);
                                }
                                
                                console.log(`Position ${index}:`, {
                                    name: position.PositionName,
                                    id: posId,
                                    departmentId: position.DepartmentId,
                                    sectionId: position.SECTIONId
                                });
                                
                                positionSelect.append(`<option value="${posId}" 
                                    data-department="${position.DepartmentId}" 
                                    data-section="${position.SECTIONId}">
                                    ${position.PositionName}
                                </option>`);
                            });
                        }
                        
                        positionSelect.trigger('change');
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading positions:', error);
                        console.error('Response:', xhr.responseText);
                        showErrorMessage('Failed to load positions. Please refresh the page and try again.');
                    }
                });
                
                // When position is changed, update department and section IDs
                $('#position').on('change', function() {
                    const selectedOption = $(this).find('option:selected');
                    const selectedValue = selectedOption.val();
                    const selectedText = selectedOption.text().trim();
                    
                    // Store the position name in a hidden field
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'positionName',
                        name: 'positionName',
                        value: selectedText
                    }).appendTo('#eventApplicationForm');
                    
                    // Debug the selected option
                    console.log('Selected option:', {
                        text: selectedText,
                        value: selectedValue
                    });
                    
                    // Check if we have a valid selection
                    if (selectedValue && selectedValue !== "undefined" && selectedValue !== "") {
                        const departmentId = selectedOption.data('department');
                        const sectionId = selectedOption.data('section');
                        
                        // Update hidden fields with the IDs
                        $('#departmentId').val(departmentId || 1);
                        $('#sectionId').val(sectionId || 1);
                        
                        // Log the exact values being used
                        console.log('Selected position data:', {
                            positionName: selectedText,
                            positionId: selectedValue,
                            departmentId: departmentId,
                            sectionId: sectionId
                        });
                    } else {
                        console.log('No valid position selected. Raw value:', selectedValue);
                        // Set default values
                        $('#departmentId').val(1);
                        $('#sectionId').val(1);
                    }
                });
            }
            
            // Form validation
            function validateForm() {
                let isValid = true;
                
                // Reset previous validation
                $('.is-invalid').removeClass('is-invalid shake pulse');
                $('.is-valid').removeClass('is-valid');
                
                // Validate each required field
                $('form input[required], form select[required]').each(function() {
                    if (!validateField($(this))) {
                        isValid = false;
                        // Add shake effect to invalid fields
                        $(this).addClass('shake');
                        // Add pulse effect for empty required fields
                        if ($(this).val() === '') {
                            $(this).addClass('pulse');
                        }
                        
                        // Remove shake class after animation completes
                        setTimeout(() => {
                            $(this).removeClass('shake');
                        }, 600);
                    }
                });
                
                // Extra validation for National ID to match database constraints
                if ($('#idType').val() === 'national') {
                    const nationalId = $('#nationalId').val().trim();
                    
                    // Check if it's exactly 14 digits
                    if (!/^\d{14}$/.test(nationalId)) {
                        $('#nationalId').addClass('is-invalid shake');
                        $('#idFeedback').text('National ID must be exactly 14 digits');
                        isValid = false;
                    }
                    
                    // Additional validation for the Egyptian National ID format
                    // First digit must be 2 or 3, second two digits must be valid month (1-12)
                    else {
                        const firstDigit = parseInt(nationalId.charAt(0));
                        const monthDigits = parseInt(nationalId.substring(3, 5));
                        
                        let idValid = true;
                        let errorMsg = '';
                        
                        // Check first digit (2 or 3 for most Egyptian IDs)
                        if (firstDigit !== 2 && firstDigit !== 3) {
                            idValid = false;
                            errorMsg = 'Egyptian National ID usually starts with 2 or 3';
                        }
                        // Check month portion (01-12)
                        else if (monthDigits < 1 || monthDigits > 12) {
                            idValid = false;
                            errorMsg = 'Month portion of ID (positions 4-5) must be between 01-12';
                        }
                        
                        if (!idValid) {
                            $('#nationalId').addClass('is-invalid shake');
                            $('#idFeedback').text(errorMsg);
                            isValid = false;
                        }
                    }
                }
                
                // Extra validation for email if provided
                const emailValue = $('#email').val().trim();
                if (emailValue && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
                    $('#email').addClass('is-invalid shake');
                    isValid = false;
                }
                
                // Validate CV file size if uploaded
                const fileInput = $('#cvFile')[0];
                if (fileInput.files.length > 0) {
                    const fileSize = fileInput.files[0].size / 1024 / 1024; // Convert to MB
                    if (fileSize > 5) {
                        showWarningMessage('CV file size must be less than 5MB');
                        isValid = false;
                    }
                }
                
                return isValid;
            }
            
            // Function to submit form with retry logic
            function submitFormWithRetry(formData, attempt, maxAttempts) {
                $('#submitBtn').addClass('is-loading').prop('disabled', true);
                
                // Create a complete name from first and last name
                const fullName = $('#firstName').val().trim() + ' ' + $('#lastName').val().trim();
                formData.set('Name', fullName);
                
                // Get the selected position data
                const selectedOption = $('#position').find('option:selected');
                const positionId = selectedOption.val();
                const positionName = selectedOption.text().trim();
                
                // CRITICAL FIX: Always use the position name for Sub_Position
                if (positionName && positionName !== "Select Position") {
                    formData.set('Sub_Position', positionName);
                } else {
                    formData.set('Sub_Position', "Event Applicant");
                }
                
                // Set other position-related fields
                formData.set('Main_Position', $('#departmentId').val() || 1);
                formData.set('SECTIONId', $('#sectionId').val() || 1);
                
                // Add form metadata for events
                formData.append('isEvent', 'true');
                
                // CRITICAL FIX: Force the application status to Event in multiple ways
                // 1. Set it directly in the form data
                formData.set('Application_Status', 'Event');
                
                // 2. Add a special field to signal the backend this is from an event
                formData.set('event_application', 'true');
                
                // 3. Set it in a differently-named field in case the backend looks for this
                formData.set('Status', 'Event');
                
                console.log('CRITICAL: Setting Application_Status to "Event"');
                
                // Handle passport vs national ID correctly
                const idType = $('#idType').val();
                if (idType === 'passport') {
                    // Store the passport number in the correct field
                    formData.set('Passport_Number', $('#nationalId').val().trim());
                    
                    // Use a valid dummy National ID to satisfy the database constraint
                    formData.set('National_ID', '29912010000000');
                }
                
                // CRITICAL FIX: Handle empty email to satisfy CHK_Email constraint
                const emailValue = $('#email').val().trim();
                if (!emailValue) {
                    // Set a placeholder email that satisfies the format requirement but clearly indicates it's not a real email
                    formData.set('Email', '<EMAIL>');
                } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
                    // If email is provided but invalid, set a default format
                    formData.set('Email', '<EMAIL>');
                }
                
                // DEBUG: Log the exact form data being sent
                console.log('FORM SUBMISSION DATA:');
                for (let pair of formData.entries()) {
                    console.log(`${pair[0]}: ${pair[1]}`);
                }
                
                $.ajax({
                    url: '/api/save-candidate',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    // Add explicit headers to signal this is an event form submission
                    headers: {
                        'X-Event-Form': 'true',
                        'X-Application-Status': 'Event'
                    },
                    success: function(response) {
                        $('#submitBtn').removeClass('is-loading').prop('disabled', false);
                        
                        if (response.success) {
                            console.log('SUCCESS RESPONSE:', response);
                            
                            // If CV was uploaded, handle that separately
                            const fileInput = $('#cvFile')[0];
                            if (fileInput.files.length > 0) {
                                uploadCV(response.candidateId, fileInput.files[0]);
                            } else {
                                showSuccessMessage('Application submitted successfully!');
                                resetForm();
                            }
                        } else {
                            showErrorMessage('Failed to submit application: ' + (response.error || 'Unknown error'));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error details:', xhr.responseText);
                        
                        if (attempt < maxAttempts) {
                            // Retry submission
                            setTimeout(function() {
                                submitFormWithRetry(formData, attempt + 1, maxAttempts);
                            }, 2000);
                        } else {
                            $('#submitBtn').removeClass('is-loading').prop('disabled', false);
                            
                            console.error('Error submitting form:', xhr.responseText);
                            showErrorMessage('Failed to submit application. Please try again later.');
                        }
                    }
                });
            }
            
            // Function to upload CV
            function uploadCV(candidateId, file) {
                const cvFormData = new FormData();
                cvFormData.append('cvFile', file);
                cvFormData.append('candidateId', candidateId);
                cvFormData.append('departmentId', '1'); // Default department for events
                cvFormData.append('positionId', $('#position').val());
                cvFormData.append('candidateName', $('#firstName').val().trim() + ' ' + $('#lastName').val().trim());
                
                $.ajax({
                    url: '/upload-cv',
                    method: 'POST',
                    data: cvFormData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            showSuccessMessage('Application submitted successfully!');
                            resetForm();
                        } else {
                            showWarningMessage('Your application was submitted, but CV upload failed. You can update your CV later.');
                            resetForm();
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error uploading CV:', xhr.responseText);
                        showWarningMessage('Your application was submitted, but CV upload failed. You can update your CV later.');
                        resetForm();
                    }
                });
            }
            
            // Function to reset form
            function resetForm() {
                $('#eventApplicationForm')[0].reset();
                $('#position').val('').trigger('change');
                $('#gender').val('').trigger('change');
                $('#idType').val('national').trigger('change');
                $('.custom-file-label').text('Choose file');
                $('.is-valid').removeClass('is-valid');
            }
            
            // SweetAlert functions
            function showSuccessMessage(message) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: message,
                    confirmButtonColor: '#2c5282',
                    timer: 3000,
                    timerProgressBar: true
                });
            }
            
            function showErrorMessage(message) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: message,
                    confirmButtonColor: '#2c5282'
                });
            }
            
            function showWarningMessage(message) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Warning',
                    text: message,
                    confirmButtonColor: '#2c5282'
                });
            }
        });
    </script>
</body>
</html> 