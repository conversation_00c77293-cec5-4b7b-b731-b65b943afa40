/**
 * Token Handler Script
 * Ensures tokens from URL are properly handled during form submission
 */

// When the page loads, check for token in URL
document.addEventListener('DOMContentLoaded', function() {
    // Extract token from URL if present
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    
    if (token) {
        console.log('Token detected in URL:', token);
        
        // Store token in a hidden input
        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.id = 'completionToken';
        tokenInput.name = 'completionToken';
        tokenInput.value = token;
        
        // Add to the document
        document.body.appendChild(tokenInput);
        
        // Add token indicator to the UI
        const notificationBar = document.getElementById('notificationBar');
        const notificationMessage = document.getElementById('notificationMessage');
        
        if (notificationBar && notificationMessage) {
            notificationBar.style.display = 'block';
            notificationBar.style.backgroundColor = '#d1ecf1';
            notificationBar.style.color = '#0c5460';
            notificationBar.style.borderColor = '#bee5eb';
            
            notificationMessage.innerHTML = '<i class="fas fa-info-circle"></i> You are updating your application with a special link. Your changes will be saved to your existing application.';
            
            // Auto-hide after 10 seconds
            setTimeout(function() {
                notificationBar.style.display = 'none';
            }, 10000);
        }
    }
});

// Helper function to check if we're in token update mode
function isTokenUpdate() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.has('token');
}

// Make the function available globally
window.isTokenUpdate = isTokenUpdate; 