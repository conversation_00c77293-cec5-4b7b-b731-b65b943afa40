// Configure important variables for form submission
let isSubmitting = false;
let lastSubmissionTime = 0;
const SUBMISSION_COOLDOWN = 3000; // 3 seconds cooldown between submissions

// Function to show warning messages
function showWarningMessage(message) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'warning',
            title: 'Please Wait',
            text: message,
            confirmButtonColor: '#008D5C'
        });
    } else {
        alert(message);
    }
}

// Add this at the beginning of the file to ensure submitForm is globally available
window.submitForm = function() {
    console.log('Global submitForm called');
    
    // Check if already submitting
    if (isSubmitting) {
        console.log('Submission already in progress, ignoring duplicate request');
        showWarningMessage('Your application is already being submitted. Please wait...');
        return;
    }
    
    // Check for submission cooldown
    const now = Date.now();
    if (now - lastSubmissionTime < SUBMISSION_COOLDOWN) {
        console.log('Submission cooldown active, ignoring rapid request');
        showWarningMessage('Please wait a moment before trying to submit again.');
        return;
    }
    
    // Mark as submitting and update last submission time
    isSubmitting = true;
    lastSubmissionTime = now;
    
    // Show loading spinner
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const submitSpinner = document.getElementById('submitSpinner');
    
    if (submitBtn) submitBtn.disabled = true;
    if (submitText) submitText.style.display = 'none';
    if (submitSpinner) submitSpinner.style.display = 'inline-block';
    
    try {
        // Run validation checks first
        const requiredFields = [
            { id: 'firstName', name: 'First Name' },
            { id: 'nationalId', name: 'National ID' },
            { id: 'phoneNumber', name: 'Phone Number' },
            { id: 'email', name: 'Email' }
        ];
        
        // Check each required field
        for (const field of requiredFields) {
            const element = document.getElementById(field.id);
            if (!element || !element.value || element.value.trim() === '') {
                throw new Error(`${field.name} is required`);
            }
        }
        
        // Check position selection
        const positionSelect = document.getElementById('position');
        if (!positionSelect || !positionSelect.value) {
            throw new Error('Please select a position');
        }
        
        // Get the form data with validation
        const formData = collectFormData();
        
        // Check for network connectivity
        if (!navigator.onLine) {
            isSubmitting = false; // Reset submission status
            showErrorMessage('You appear to be offline. Please check your internet connection and try again.');
            resetSubmitButton();
            return;
        }
        
        // Log the data being sent
        console.log('Submitting form data:', formData);
        
        // Submit the form with retry logic for mobile
        submitFormWithRetry(formData, 0, 3);
    } catch (error) {
        // Reset submission status on error
        isSubmitting = false;
        
        console.error('Validation error:', error);
        showErrorMessage(error.message || 'Please check the form for errors and try again.');
        resetSubmitButton();
    }
};

// Helper function to show error messages
function showErrorMessage(message) {
    // Reset submission status
    isSubmitting = false;
    
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'error',
            title: 'Submission Error',
            text: message,
            confirmButtonColor: '#008D5C'
        });
    } else {
        alert(message);
    }
}

// Reset the submit button
function resetSubmitButton() {
    // Reset submission status
    isSubmitting = false;
    
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const submitSpinner = document.getElementById('submitSpinner');
    
    if (submitBtn) submitBtn.disabled = false;
    if (submitText) submitText.style.display = 'inline-block';
    if (submitSpinner) submitSpinner.style.display = 'none';
}

// Modify the submitFormWithRetry function to skip SharePoint API submission
function submitFormWithRetry(formData, attempt, maxAttempts) {
    console.log(`Attempt ${attempt + 1} of ${maxAttempts}`);
    
    // Add additional debug info for this submission attempt
    const debugInfo = {
        timestamp: new Date().toISOString(),
        attempt: attempt + 1,
        maxAttempts: maxAttempts,
        formData: {
            name: formData.Name,
            nationalId: formData.National_ID,
            position: formData.Sub_Position,
            department: formData.Main_Position
        }
    };
    console.log('Submission debug info:', debugInfo);
    
    // First save to database
    fetch('/api/save-candidate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(errorData => {
                console.error('Server error details:', errorData);
                throw new Error(errorData.error || errorData.details || 'Failed to save candidate data');
            });
        }
        return response.json();
    })
    .then(result => {
        console.log('Database save result:', result);
        
        // SKIP SharePoint API submission and show success directly
        showSuccessMessage('Your application has been submitted successfully! Thank you');
        
        /* COMMENTED OUT - Skip SharePoint API submission
        // Now submit the form to the final endpoint
        return fetch('/submit-application', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        */
    })
    /*
    .then(response => {
        if (!response.ok) {
            return response.json().then(errorData => {
                console.error('Submit application error details:', errorData);
                throw new Error(errorData.error || errorData.message || 'Failed to submit application');
            });
        }
        return response.json();
    })
    .then(data => {
        // Show success message
        showSuccessMessage('Your application has been submitted successfully!');
    })
    */
    .catch(error => {
        console.error('Submission error:', error);
        
        if (attempt < maxAttempts - 1) {
            // Wait and retry with exponential backoff
            const backoffTime = Math.pow(2, attempt) * 1000;
            console.log(`Retrying in ${backoffTime}ms...`);
            
            setTimeout(() => {
                submitFormWithRetry(formData, attempt + 1, maxAttempts);
            }, backoffTime);
        } else {
            // Show error after max attempts
            showErrorMessage(error.message || 'An error occurred during submission. Please try again.');
            resetSubmitButton();
        }
    });
}

// Add a helper function to show success messages
function showSuccessMessage(message) {
    // Reset submission status
    isSubmitting = false;
    resetSubmitButton();
    
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: message,
            confirmButtonColor: '#008D5C'
        }).then(() => {
            // Redirect to home or reset form
            window.location.href = '/';
        });
    } else {
        alert(message);
        window.location.href = '/';
    }
}

// Function to collect form data
function collectFormData() {
    const formData = {};
    
    // Get all form inputs
    const inputs = document.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        if (input.id && input.id !== '') {
            // Basic sanitization - trim whitespace and prevent empty strings
            let value = input.value.trim();
            formData[input.id] = value !== '' ? value : null;
        }
    });
    
    // Check for token in URL and include it in the form data
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    
    if (token) {
        console.log('Including token in form data from URL:', token);
        formData.completionToken = token;
    }
    
    // Also check if we have a hidden token input
    const tokenInput = document.getElementById('completionToken');
    if (tokenInput && tokenInput.value) {
        console.log('Including token in form data from hidden input:', tokenInput.value);
        formData.completionToken = tokenInput.value;
    }
    
    // Format the data for backend
    formData.Name = `${formData.firstName} ${formData.secondName} ${formData.thirdName}`.trim();
    formData.Mobile = formData.phoneNumber;
    formData.Current_Address = formData.address;
    formData.Email = formData.email;
    formData.Age = parseInt(formData.age) || 0;
    formData.Military_Status = formData.conscriptionStatus;
    formData.Marital_Status = formData.socialStatus;
    formData.Religion = formData.religion;
    formData.Current_Employer = formData.currentEmployer;
    formData.Job_Title = formData.jobTitle;
    
    // Ensure salary values are valid numbers
    formData.Current_Salary = formData.currentSalary ? parseFloat(formData.currentSalary.replace(/[^\d.-]/g, '')) || 0 : 0;
    formData.Leave_Reason = formData.reasonForLeaving;
    formData.EXP_Years_From = formData.from;
    formData.EXP_Years_To = formData.to;
    formData.Expected_Salary = formData.expectedSalary ? parseFloat(formData.expectedSalary.replace(/[^\d.-]/g, '')) || 0 : 0;
    formData.Grad_Year = formData.yearOfGraduation;
    formData.Grade = formData.grade;
    formData.EMP_ENG_LVL = formData.englishLevel;
    formData.EMP_PC_LVL = formData.computerSkills;
    formData.Has_Relative_In_Hospital = formData.relativeInHospital;
    formData.Worked_At_Shifa_Before = formData.workAtShifa;
    formData.Has_Chronic_Disease = formData.chronicDiseases;
    formData.Gender = formData.gender;
    formData.Hear_About_Us = formData.hearAboutUs;
    
    // Handle ID Type
    formData.ID_Type = formData.idType || 'nationalId';
    
    if (formData.ID_Type === 'passport') {
        // Store original passport number
        formData.Passport_Number = formData.nationalId;
        
        // Create numeric version for National_ID field (must pass CHECK constraint)
        // Format: 9999 followed by padded digits to reach 14 digits total
        let numericPassport = '';
        
        // Start with prefix 9999 to indicate passport
        numericPassport = '9999';
        
        // Take up to 10 digits from the passport, or pad with zeros
        const passportDigits = formData.nationalId.replace(/\D/g, ''); // Remove non-digits
        const paddedPassportDigits = (passportDigits + '0000000000').substring(0, 10);
        
        // Create the 14-digit National_ID
        formData.National_ID = numericPassport + paddedPassportDigits;
        
        console.log('Formatted passport for DB:', formData.National_ID);
    } else {
        // Regular National ID processing
        formData.National_ID = formData.nationalId;
        formData.Passport_Number = null;
    }
    
    // Add other necessary fields from the form
    formData.Main_Position = parseInt(document.getElementById('departmentId').value) || 0;
    formData.SECTIONId = parseInt(document.getElementById('sectionId').value) || 0;
    
    // Use the position text value instead of ID for Sub_Position
    const positionElement = document.getElementById('position');
    if (positionElement) {
        // Get the text value from the selected option
        if (positionElement.selectedIndex >= 0) {
            formData.Sub_Position = positionElement.options[positionElement.selectedIndex].text;
        } else {
            formData.Sub_Position = positionElement.value; // Fallback to value if no option is selected
        }
    }
    
    // Add qualification data
    if (document.getElementById('specialization')) {
        formData.Category_ID = parseInt(document.getElementById('specialization').value) || 0;
        formData.CategoryName_AR = $("#specialization option:selected").text();
    }
    
    if (document.getElementById('faculty')) {
        formData.FID = parseInt(document.getElementById('faculty').value) || 0;
        formData.Faculty = $("#faculty option:selected").text();
    }
    
    if (document.getElementById('university')) {
        // Get the university name
        formData.uniname_ar = $("#university option:selected").text();
        
        // Get the university ID and set it as UID
        formData.UID = parseInt(document.getElementById('university').value) || 0;
        console.log('Setting UID from university selection:', formData.UID);
    }
    
    if (document.getElementById('scientificDegree')) {
        formData.Grade_ID = 1; // Default, can be updated if available
        formData.GradeName_AR = document.getElementById('scientificDegree').value;
    }
    
    return formData;
}

console.log('Script starting...');

// Add jQuery selector extension for case-insensitive contains
$.expr[':'].containsInsensitive = function(a, i, m) {
    return jQuery(a).text().toUpperCase()
        .indexOf(m[3].toUpperCase()) >= 0;
};

// Add jQuery selector for text contains
$(document).ready(function() {
    // Add custom selector for finding text
    jQuery.expr[':'].contains = function(a, i, m) {
        return jQuery(a).text().indexOf(m[3]) >= 0;
    };
});

// Check if completionToken is already defined
if (typeof completionToken === 'undefined') {
    completionToken = null;
}

// Add validation icon update function
function updateValidationIcon(element) {
    if (element.val()) {
        element.addClass('is-valid').removeClass('is-invalid');
    } else {
        element.addClass('is-invalid').removeClass('is-valid');
    }
}

function checkForToken() {
    // First check if the token came from the server-side (EJS)
    const serverData = document.getElementById('server-data');
    if (serverData && serverData.getAttribute('data-has-token') === 'true') {
        completionToken = serverData.getAttribute('data-token');
        console.log('Token detected from server data:', completionToken);
        return true;
    }
    
    // Otherwise check URL params
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    
    if (token) {
        completionToken = token;
        console.log('Token detected from URL:', token);
        return true;
    }
    
    return false;
}

// Add the forcePositionSelection function to the global scope
function forcePositionSelection(positionText) {
    // First clear and highlight the position field to indicate it's being processed
    $('#position').css('background-color', '#ffffe0').val('').trigger('change');
    
    console.log('Attempting to force select position:', positionText);
    
    // If selectPositionByText is available, use it first
    if (window.selectPositionByText) {
        console.log('Using position-selector.js to select position');
        if (window.selectPositionByText(positionText)) {
            console.log('Successfully selected position using position-selector.js');
            $('#position').css('background-color', '').addClass('is-valid').removeClass('is-invalid');
            return;
        }
    }
    
    // Fallback to original method if position-selector.js didn't work
    // Try multiple times with increasing delays
    const attempts = [500, 1000, 1500, 2000, 3000];
    
    attempts.forEach((delay, index) => {
        setTimeout(() => {
            console.log(`Position selection attempt ${index + 1}...`);
            
            // Highlight the select to show it's being processed
            $('#position').css('background-color', '#f0fff0');
            
            // Special handling for "اخصائي أول برمجيات"
            if (positionText.includes('اخصائي') && positionText.includes('برمجيات')) {
                console.log('Detected أخصائي برمجيات special case');
                // Look specifically for any option containing this text
                let foundProgrammerOption = false;
                $('#position option').each(function() {
                    const optionText = $(this).text().trim();
                    if (optionText.includes('اخصائي') || optionText.includes('برمجيات')) {
                        $(this).prop('selected', true);
                        $('#position').val($(this).val()).trigger('change');
                        console.log('Selected أخصائي برمجيات match:', optionText);
                        foundProgrammerOption = true;
                        return false; // break the loop
                    }
                });
                
                if (foundProgrammerOption) {
                    // Skip other selection methods if we found a programmer position
                    return;
                }
            }
            
            // First try: direct value setting by text
            $('#position').val(positionText).trigger('change');
            
            // If that didn't work, try to find by option text
            if (!$('#position').val()) {
                let bestMatch = null;
                let bestMatchScore = 0;
                
                $('#position option').each(function() {
                    if ($(this).css('display') === 'none') return; // Skip hidden options
                    
                    const optionText = $(this).text().trim();
                    // Check for exact match
                    if (optionText === positionText) {
                        bestMatch = $(this);
                        bestMatchScore = 100;
                        return false; // Break the loop
                    }
                    
                    // Check for contains match
                    if (optionText.includes(positionText) || positionText.includes(optionText)) {
                        const matchLength = Math.min(optionText.length, positionText.length);
                        const score = matchLength * 2;
                        if (score > bestMatchScore) {
                            bestMatch = $(this);
                            bestMatchScore = score;
                        }
                    }
                });
                
                if (bestMatch) {
                    // Select the best matching option
                    bestMatch.prop('selected', true);
                    $('#position').val(bestMatch.val()).trigger('change');
                    console.log('Selected position via best match:', bestMatch.text());
                }
            }
            
            // If still no match and this is the last attempt, select the first non-empty option
            if (!$('#position').val() && index === attempts.length - 1) {
                const firstOption = $('#position option').not('[value=""]').first();
                if (firstOption.length) {
                    firstOption.prop('selected', true);
                    $('#position').val(firstOption.val()).trigger('change');
                    console.log('Forced selection of first available position:', firstOption.text());
                }
            }
            
            // Restore normal background and add validation classes
            if ($('#position').val()) {
                $('#position').css('background-color', '').addClass('is-valid').removeClass('is-invalid');
            }
        }, delay);
    });
}

// Add this function after forcePositionSelection at the top of the file
function simulatePositionClick(positionText) {
    console.log('Attempting direct UI interaction for position:', positionText);
    
    // Look for visible position elements in the list
    setTimeout(() => {
        // Try to find visible elements containing the text using jQuery
        try {
            // First, try to directly interact with the positions list
            const positionsList = document.getElementById('positionsList');
            if (positionsList) {
                // Check if positions exist in the positions list
                const positionItems = positionsList.querySelectorAll('.position-item');
                console.log(`Found ${positionItems.length} position items in #positionsList`);
                
                // Look for a matching position item
                for (const item of positionItems) {
                    const itemText = item.textContent.trim();
                    if (itemText.includes(positionText)) {
                        console.log(`Found matching position item: "${itemText}"`);
                        item.click();
                        return;
                    }
                }
                
                // If we have no match but there are items, click the first one as fallback
                if (positionItems.length > 0) {
                    console.log('No exact match, clicking first position item');
                    positionItems[0].click();
                    return;
                }
            }
            
            // If direct interaction with positions list failed, try jQuery approach
            let positionElements = $(':contains("' + positionText + '")').filter(':visible');
            console.log(`Found ${positionElements.length} visible elements containing "${positionText}"`);
            
            if (positionElements.length === 0) {
                // Try with just the "اخصائي" part
                positionElements = $(':contains("اخصائي")').filter(':visible');
                console.log(`Found ${positionElements.length} visible elements containing "اخصائي"`);
            }
            
            if (positionElements.length === 0) {
                // Try with just the "برمجيات" part
                positionElements = $(':contains("برمجيات")').filter(':visible');
                console.log(`Found ${positionElements.length} visible elements containing "برمجيات"`);
            }
            
            // Focus on positions specifically
            let positionClicked = false;
            positionElements.each(function() {
                if (positionClicked) return;
                
                const $element = $(this);
                const text = $element.text().trim();
                console.log(`Examining element: "${text}" (${$element.prop('tagName')})`);
                
                // Check if this might be a position element based on element type and text
                if (($element.is('li, div.position, span.position-name, td, a, .position-item, .position-title') || 
                     $element.hasClass('position') || 
                     $element.hasClass('position-item') ||
                     $element.parent().hasClass('position') ||
                     text.length < 100) && // Likely a position name, not a long text
                    (text.includes('اخصائي'))) {
                    
                    console.log(`Clicking position element: ${text}`);
                    $element.trigger('click');
                    positionClicked = true;
                    
                    // Also try to directly set any nearby hidden fields
                    const positionField = $('#position');
                    if (positionField.length) {
                        positionField.val(text).trigger('change');
                        console.log(`Directly set #position field to: ${text}`);
                    }
                    
                    return false; // break each loop
                }
            });
            
            // If we still haven't found anything, try the fallback approach
            if (!positionClicked) {
                // Use jQuery instead of querySelectorAll for custom selectors
                const positionNodes = $('.item-list .position-item, .position-list .item, .positions-list .position-item')
                    .add($('li').filter(function() { 
                        return $(this).text().includes('اخصائي'); 
                    }))
                    .add($('div').filter(function() { 
                        return $(this).text().includes('اخصائي'); 
                    }));
                
                console.log(`Found ${positionNodes.length} position items via jQuery`);
                
                // Click the first matching one
                if (positionNodes.length > 0) {
                    const node = positionNodes[0];
                    console.log(`Clicking first position: ${$(node).text().trim()}`);
                    $(node).click();
                    
                    // If it's a list item, also try to click any links inside it
                    const links = $(node).find('a');
                    if (links.length > 0) {
                        console.log('Clicking link inside position element');
                        links.first().click();
                    }
                } else {
                    console.log('No position elements found to click');
                    
                    // Last resort: directly set the position select value if we have a specific position
                    if (positionText === 'اخصائي برمجيات') {
                        $('#position').val('اخصائي برمجيات').trigger('change');
                        $('#position').addClass('is-valid').removeClass('is-invalid');
                        console.log('Manually set position select to اخصائي برمجيات');
                    }
                }
            }
        } catch (e) {
            console.error('Error in position simulation:', e);
        }
    }, 1000);
}

function prefillFormWithCandidateData(candidateData) {
    console.log('Starting form prefill with data:', candidateData);
    
    // Handle name fields - Split the full name into parts
    if (candidateData.Name) {
        const nameParts = candidateData.Name.trim().split(/\s+/);
        console.log('Setting name fields with parts:', nameParts);
        $('#firstName').val(nameParts[0] || '').addClass('is-valid').removeClass('is-invalid');
        $('#secondName').val(nameParts[1] || '').addClass('is-valid').removeClass('is-invalid');
        $('#thirdName').val(nameParts[2] || '').addClass('is-valid').removeClass('is-invalid');
    }
    
    console.log('Setting National ID:', candidateData.National_ID);
    $('#nationalId').val(candidateData.National_ID || '').addClass('is-valid').removeClass('is-invalid');
    
    console.log('Setting Email:', candidateData.Email);
    $('#email').val(candidateData.Email || '').addClass('is-valid').removeClass('is-invalid');
    
    // Map Mobile to phoneNumber field
    console.log('Setting Phone Number:', candidateData.Mobile);
    $('#phoneNumber').val(candidateData.Mobile || '').addClass('is-valid').removeClass('is-invalid');
    
    $('#address').val(candidateData.Current_Address || '').addClass('is-valid').removeClass('is-invalid');
    
    if (candidateData.Age) {
        $('#age').val(candidateData.Age).addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.Gender) {
        $('#gender').val(candidateData.Gender).trigger('change').addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.Religion) {
        $('#religion').val(candidateData.Religion).trigger('change').addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.Marital_Status) {
        $('#socialStatus').val(candidateData.Marital_Status).trigger('change').addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.Military_Status) {
        $('#conscriptionStatus').val(candidateData.Military_Status).trigger('change').addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.UID) {
        $('#university').val(candidateData.UID).trigger('change').addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.FID) {
        $('#faculty').val(candidateData.FID).trigger('change').addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.Grade_ID && candidateData.Category_ID) {
        $('#specialization').val(candidateData.Category_ID).trigger('change').addClass('is-valid').removeClass('is-invalid');
        setTimeout(() => {
            $('#scientificDegree').val(candidateData.Grade_ID).trigger('change').addClass('is-valid').removeClass('is-invalid');
        }, 500);
    }
    
    if (candidateData.Grade) {
        $('#grade').val(candidateData.Grade).addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.Grad_Year) {
        $('#yearOfGraduation').val(candidateData.Grad_Year).addClass('is-valid').removeClass('is-invalid');
    }
    
    $('#currentEmployer').val(candidateData.Current_Employer || '').addClass('is-valid').removeClass('is-invalid');
    $('#jobTitle').val(candidateData.Job_Title || '').addClass('is-valid').removeClass('is-invalid');
    
    if (candidateData.EXP_Years_From) {
        $('#from').val(new Date(candidateData.EXP_Years_From).toISOString().split('T')[0]).addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.EXP_Years_To) {
        $('#to').val(new Date(candidateData.EXP_Years_To).toISOString().split('T')[0]).addClass('is-valid').removeClass('is-invalid');
    }
    
    $('#currentSalary').val(candidateData.Current_Salary || '').addClass('is-valid').removeClass('is-invalid');
    $('#reasonForLeaving').val(candidateData.Leave_Reason || '').addClass('is-valid').removeClass('is-invalid');
    
    if (candidateData.Hear_About_Us) {
        $('#hearAboutUs').val(candidateData.Hear_About_Us).trigger('change').addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.EMP_ENG_LVL) {
        $('#englishLevel').val(candidateData.EMP_ENG_LVL).trigger('change').addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.EMP_PC_LVL) {
        $('#computerSkills').val(candidateData.EMP_PC_LVL).trigger('change').addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.Has_Relative_In_Hospital) {
        $('#relativeInHospital').val(candidateData.Has_Relative_In_Hospital ? "Yes" : "No").trigger('change').addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.Worked_At_Shifa_Before) {
        $('#workAtShifa').val(candidateData.Worked_At_Shifa_Before ? "Yes" : "No").trigger('change');
    }
    
    if (candidateData.Has_Chronic_Disease) {
        $('#chronicDiseases').val(candidateData.Has_Chronic_Disease ? "Yes" : "No").trigger('change');
    }
    
    // Set date of birth, birthplace if data exists
    if (candidateData.Birth_Date) {
        $('#dateOfBirth').val(candidateData.Birth_Date).addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.Birth_Place) {
        $('#governorate').val(candidateData.Birth_Place).addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.City) {
        $('#city').val(candidateData.City).trigger('change').addClass('is-valid').removeClass('is-invalid');
    }
    
    if (candidateData.Main_Position) {
        // First set the department and make sure the change event fully completes
        $('#department').val(candidateData.Main_Position).trigger('change').addClass('is-valid').removeClass('is-invalid');
        
        // Use a longer timeout to ensure positions are loaded for the selected department
        setTimeout(() => {
            if (candidateData.Sub_Position) {
                const positionText = candidateData.Sub_Position;
                console.log('Set position to:', positionText);
                
                // First try the automated approaches
                forcePositionSelection(positionText);
                simulatePositionClick(positionText);
                
                // Now try specifically for the pattern shown in the screenshot
                setTimeout(() => {
                    // The screenshot shows a list of positions with text content like "اخصائي استقبال اشعة"
                    // Let's try to click directly on these list items
                    try {
                        console.log('Attempting screenshot-specific selection approach');
                        const items = document.querySelectorAll('.positions-list li, .item-list li, tr.position');
                        
                        console.log(`Found ${items.length} list items to check`);
                        let found = false;
                        
                        // Check each list item
                        items.forEach(item => {
                            if (found) return;
                            
                            const text = item.textContent.trim();
                            console.log(`List item text: "${text}"`);
                            
                            // If the item matches اخصائي استقبال or اخصائي برمجيات
                            if (text.includes('اخصائي') && (text.includes('استقبال') || text.includes('برمجيات'))) {
                                console.log(`Found matching list item: "${text}". Clicking...`);
                                item.click();
                                found = true;
                                
                                // Also try to click any child link or button in the item
                                const clickable = item.querySelector('a, button, .clickable, [role="button"]');
                                if (clickable) {
                                    console.log('Clicking child element inside list item');
                                    clickable.click();
                                }
                            }
                        });
                        
                        // If we didn't find a matching item, force validation
                        if (!found) {
                            console.log('No matching item found, forcing position as valid');
                            $('#position').addClass('is-valid').removeClass('is-invalid');
                            
                            // Try to click the first item in the list
                            if (items.length > 0) {
                                console.log(`Clicking first item in list: "${items[0].textContent.trim()}"`);
                                items[0].click();
                            }
                        }
                    } catch (e) {
                        console.error('Error in screenshot-specific approach:', e);
                    }
                }, 2500);
                
                // The final check with all DOM elements
                setTimeout(() => {
                    // Find and click the position directly in the rendered list
                    const allPositionElements = document.querySelectorAll('li, div, span, a, td');
                    console.log(`Checking ${allPositionElements.length} potential position elements...`);
                    
                    let found = false;
                    allPositionElements.forEach(el => {
                        if (found) return;
                        
                        const text = el.textContent.trim();
                        if (text && 
                            (text.includes('اخصائي أول برمجيات') || 
                             (text.includes('اخصائي') && text.includes('برمجيات')))) {
                            console.log('Found direct position element:', text);
                            console.log('Clicking element:', el);
                            el.click();
                            found = true;
                        }
                    });
                    
                    // Final validation for any selected position
                    $('#position').addClass('is-valid').removeClass('is-invalid');
                }, 3000);
            }
        }, 2000); // Increase timeout to give more time for options to load
    }
    
    if (candidateData.Expected_Salary) {
        $('#expectedSalary').val(candidateData.Expected_Salary).addClass('is-valid').removeClass('is-invalid');
    }
    
    // Don't call updateValidationIcon function since we're directly adding validation classes
    // $('input, select').each(function() {
    //     updateValidationIcon($(this));
    // });
    
    // Force all filled fields to be valid
    setTimeout(function() {
        $('input, select').each(function() {
            if ($(this).val() && $(this).val().trim() !== '') {
                $(this).addClass('is-valid').removeClass('is-invalid');
            }
        });
    }, 1000);
}

// Make the function globally available
window.prefillFormWithCandidateData = prefillFormWithCandidateData;

$(document).ready(function() {
    // Language switching functionality
    const toggle = document.getElementById("toggle");
    const flag = document.getElementById("flag");
    
    // Define translations
    const translations = {
        en: {
            // Personal Information Section
            title: "Shifa Hospital Job Application",
            personalInfo: "Personal Information",
            fullName: "Full Name",
            phoneNumber: "Phone Number",
            idType: "ID Type",
            nationalId: "National ID",
            passportNumber: "Passport Number",
            age: "Age",
            birthDate: "Birth Date",
            birthPlace: "Birth Place",
            gender: "Gender",
            religion: "Religion",
            religionOptions: {
                muslim: "Muslim",
                christian: "Christian",
                other: "Other"
            },
            address: "Address",
            selectCity: "Select City",
            maritalStatus: "Marital Status",
            email: "Email Address",
            militaryStatus: "Military Status",
            cities: {
                cairo: "Cairo",
                alexandria: "Alexandria",
                portSaid: "Port Said",
                suez: "Suez",
                damietta: "Damietta",
                dakahlia: "Dakahlia", 
                sharqia: "Sharqia",
                qalyubia: "Qalyubia",
                kafrElSheikh: "Kafr El Sheikh",
                gharbia: "Gharbia",
                monufia: "Monufia",
                beheira: "Beheira",
                ismailia: "Ismailia",
                giza: "Giza",
                beniSuef: "Beni Suef",
                faiyum: "Faiyum",
                minya: "Minya",
                asyut: "Asyut",
                sohag: "Sohag"
            },
            
            // Educational Section
            educational: "Educational",
            selectSpecialization: "Select Specialization",
            scientificDegree: "Scientific Degree",
            selectUniversity: "Select University",
            selectFaculty: "Select Faculty",
            selectGrade: "Select Grade",
            gradeOptions: {
                excellent: "Excellent",
                veryGood: "Very Good",
                good: "Good",
                pass: "Pass"
            },
            graduationYear: "Year of Graduation",
            
            // Experience Section
            experience: "Experience",
            currentEmployer: "Current Employer",
            jobTitle: "Job Title",
            fromDate: "From Date",
            toDate: "To Date",
            currentSalary: "Current Salary",
            reasonForLeaving: "Reason for Leaving",
            hearAboutUs: "How did you hear about us?",
            hearAboutUsOptions: {
                socialMedia: "Social Media",
                friends: "Friends/Family",
                website: "Hospital Website",
                newspaper: "Newspaper",
                other: "Other"
            },
            
            // Position Section
            position: "Position",
            selectDepartment: "Select Department",
            selectPosition: "Select Position",
            expectedSalary: "Expected Salary",
            uploadCV: "Upload CV",
            
            // Other Information Section
            otherInfo: "Other Information",
            englishLevel: "English Level",
            englishLevelOptions: {
                fluent: "Fluent",
                veryGood: "Very Good",
                good: "Good",
                poor: "Poor"
            },
            fluent: "Fluent",
            veryGood: "Very Good",
            good: "Good",
            poor: "Poor",
            computerSkills: "Computer Skills Level",
            computerSkillsOptions: {
                excellent: "Excellent",
                veryGood: "Very Good",
                good: "Good",
                poor: "Poor"
            },
            excellent: "Excellent",
            workAtShifa: "Do you work at Shifa Hospital Before?",
            chronicDiseases: "Do you have any chronic diseases",
            relativesInHospital: "Do you have Relatives in the Hospital",
            yes: "Yes",
            no: "No",
            
            // Navigation Buttons
            previous: "Previous",
            next: "Next",
            submit: "Agree and send application",
            
            // Modal Text
            thankYou: "Thank You!",
            submitting: "Submitting your application...",
            submitSuccess: "Your application has been submitted successfully.",
            
            // Validation Messages
            required: "This field is required",
            invalidEmail: "Please enter a valid email address",
            invalidPhone: "Please enter a valid phone number",
            invalidId: "Please enter a valid National ID",
            selectFile: "Select file",
            fileType: "Please select a valid file (PDF, DOC, DOCX)",
            maritalStatusOptions: {
                single: "Single",
                married: "Married",
                divorced: "Divorced",
                widowed: "Widowed"
            },
            militaryStatusOptions: {
                completed: "Completed",
                exempted: "Exempted",
                postponed: "Postponed",
                inProgress: "In Progress",
                notApplicable: "Not Applicable"
            },
            invalidName: "Please enter a valid name using Arabic characters only",
            idTypeOptions: {
                nationalId: "National ID",
                passport: "Passport"
            }
        },
        ar: {
            // Personal Information Section
            title: "التقديم للوظائف - مستشفى شفا",
            personalInfo: "البيانات الشخصية",
            fullName: "الاسم بالكامل",
            phoneNumber: "رقم الهاتف",
            idType: "نوع الهوية",
            nationalId: "الرقم القومي",
            passportNumber: "رقم جواز السفر",
            age: "العمر",
            birthDate: "تاريخ الميلاد",
            birthPlace: "محل الميلاد",
            gender: "النوع",
            religion: "الديانة",
            religionOptions: {
                muslim: "مسلم",
                christian: "مسيحي",
                other: "أخرى"
            },
            address: "العنوان",
            selectCity: "اختر المدينة",
            maritalStatus: "الحالة الاجتماعية",
            email: "البريد الإلكتروني",
            militaryStatus: "الموقف من التجنيد",
            
            // Educational Section
            educational: "المؤهلات التعليمية",
            selectSpecialization: "اختر التخصص",
            scientificDegree: "الدرجة العلمية",
            selectUniversity: "اختر الجامعة",
            selectFaculty: "اختر الكلية",
            selectGrade: "اختر التقدير",
            gradeOptions: {
                excellent: "ممتاز",
                veryGood: "جيد جداً",
                good: "جيد",
                pass: "مقبول"
            },
            graduationYear: "سنة التخرج",
            
            // Experience Section
            experience: "الخبرات",
            currentEmployer: "جهة العمل الحالية",
            jobTitle: "المسمى الوظيفي",
            fromDate: "من تاريخ",
            toDate: "إلى تاريخ",
            currentSalary: "الراتب الحالي",
            reasonForLeaving: "سبب ترك العمل",
            hearAboutUs: "كيف سمعت عنا؟",
            hearAboutUsOptions: {
                socialMedia: "وسائل التواصل الاجتماعي",
                friends: "الأصدقاء/العائلة",
                website: "موقع المستشفى",
                newspaper: "الجريدة",
                other: "أخرى"
            },
            
            // Position Section
            position: "الوظيفة",
            selectDepartment: "اختر القسم",
            selectPosition: "اختر الوظيفة",
            expectedSalary: "الراتب المتوقع",
            uploadCV: "تحميل السيرة الذاتية",
            
            // Other Information Section
            otherInfo: "معلومات أخرى",
            englishLevel: "مستوى اللغة الإنجليزية",
            englishLevelOptions: {
                fluent: "ممتاز",
                veryGood: "جيد جداً",
                good: "جيد",
                poor: "ضعيف"
            },
            fluent: "ممتاز",
            veryGood: "جيد جداً",
            good: "جيد",
            poor: "ضعيف",
            computerSkills: "مستوى مهارات الكمبيوتر",
            computerSkillsOptions: {
                excellent: "ممتاز",
                veryGood: "جيد جداً",
                good: "جيد",
                poor: "ضعيف"
            },
            excellent: "ممتاز",
            workAtShifa: "هل سبق لك العمل في مستشفى شفا؟",
            chronicDiseases: "هل لديك أي أمراض مزمنة؟",
            relativesInHospital: "هل لديك أقارب في المستشفى؟",
            yes: "نعم",
            no: "لا",
            
            // Navigation Buttons
            previous: "السابق",
            next: "التالي",
            submit: "موافق وإرسال الطلب",
            
            // Modal Text
            thankYou: "شكراً لك!",
            submitting: "جاري إرسال طلبك...",
            submitSuccess: "تم إرسال طلبك بنجاح.",
            
            // Validation Messages
            required: "هذا الحقل مطلوب",
            invalidEmail: "يرجى إدخال بريد إلكتروني صحيح",
            invalidPhone: "يرجى إدخال رقم هاتف صحيح",
            invalidId: "يرجى إدخال رقم قومي صحيح",
            selectFile: "اختر ملف",
            fileType: "يرجى اختيار ملف صالح (PDF, DOC, DOCX)",
            selectCity: "اختر المدينة",
            cities: {
                cairo: "القاهرة",
                alexandria: "الإسكندرية",
                portSaid: "بورسعيد",
                suez: "السويس",
                damietta: "دمياط",
                dakahlia: "الدقهلية",
                sharqia: "الشرقية",
                qalyubia: "القليوبية",
                kafrElSheikh: "كفر الشيخ",
                gharbia: "الغربية",
                monufia: "المنوفية",
                beheira: "البحيرة",
                ismailia: "الإسماعيلية",
                giza: "الجيزة",
                beniSuef: "بني سويف",
                faiyum: "الفيوم",
                minya: "المنيا",
                asyut: "أسيوط",
                sohag: "سوهاج",
                qena: "قنا",
                aswan: "أسوان",
                luxor: "الأقصر",
                redSea: "البحر الأحمر",
                newValley: "الوادي الجديد",
                matrouh: "مطروح",
                northSinai: "شمال سيناء",
                southSinai: "جنوب سيناء"
            },
            maritalStatusOptions: {
                single: "أعزب",
                married: "متزوج",
                divorced: "مطلق",
                widowed: "أرمل"
            },
            militaryStatusOptions: {
                completed: "أدى الخدمة",
                exempted: "معفى",
                postponed: "مؤجل",
                inProgress: "يؤدي الخدمة",
                notApplicable: "لا ينطبق"
            },
            invalidName: "الرجاء إدخال الاسم باللغة العربية فقط",
            idTypeOptions: {
                nationalId: "رقم قومي",
                passport: "جواز سفر"
            }
        }
    };

    // Function to update page content
    function updatePageLanguage(lang) {
        const t = translations[lang];
        
        // Update name input placeholders
        $('input[name="firstName"]').attr('placeholder', lang === 'ar' ? 'الاسم الأول' : 'First Name');
        $('input[name="secondName"]').attr('placeholder', lang === 'ar' ? 'الاسم الثاني' : 'Second Name');
        $('input[name="thirdName"]').attr('placeholder', lang === 'ar' ? 'الاسم الثالث' : 'Third Name');

        // Update section headers
        $('.text-center:contains("Personal Information"), .text-center:contains("البيانات الشخصية")')
            .html(`<i class="fas fa-user"></i> ${t.personalInfo}`);
        $('.text-center:contains("Educational"), .text-center:contains("المؤهلات التعليمية")')
            .html(`<i class="fas fa-graduation-cap"></i> ${t.educational}`);
        $('.text-center:contains("Experience"), .text-center:contains("الخبرات")')
            .html(`<i class="fas fa-briefcase"></i> ${t.experience}`);
        $('.text-center:contains("Position"), .text-center:contains("الوظيفة")')
            .html(`<i class="fas fa-user-tie"></i> ${t.position}`);
        $('.text-center:contains("Other Information"), .text-center:contains("معلومات أخرى")')
            .html(`<i class="fas fa-info-circle"></i> ${t.otherInfo}`);
        
        // Update all text content
        $('h1').text(t.title);
        $('.text-center:contains("Personal Information"), .text-center:contains("البيانات الشخية")').html(`<i class="fas fa-user"></i> ${t.personalInfo}`);
        $('.text-center:contains("Educational"), .text-center:contains("المؤهلات التعليمية")').html(`<i class="fas fa-graduation-cap"></i> ${t.educational}`);
        $('.text-center:contains("Experience"), .text-center:contains("الخبرات")').html(`<i class="fas fa-briefcase"></i> ${t.experience}`);
        $('.text-center:contains("Position"), .text-center:contains("الوظيفة")').html(`<i class="fas fa-user-tie"></i> ${t.position}`);
        $('.text-center:contains("Other Information"), .text-center:contains("معلومات أخرى")').html(`<i class="fas fa-info-circle"></i> ${t.otherInfo}`);
        
        // Update all input placeholders
        $('input[name="name"]').attr('placeholder', t.fullName);
        $('#phoneNumber').attr('placeholder', t.phoneNumber);
        $('#idType option:first-child').text(t.idType);
        $('#nationalId').attr('placeholder', $('#idType').val() === 'passport' ? t.passportNumber : t.nationalId);
        $('#idTypeLabel').text($('#idType').val() === 'passport' ? t.passportNumber : t.nationalId);
        $('#email').attr('placeholder', t.email);
        $('#age').attr('placeholder', t.age);
        $('#dateOfBirth').attr('placeholder', t.birthDate);
        $('#governorate').attr('placeholder', t.birthPlace);
        $('#gender').attr('placeholder', t.gender);
        $('#address').attr('placeholder', t.address);
        $('#specialization').attr('placeholder', t.selectSpecialization);
        $('#university').attr('placeholder', t.selectUniversity);
        $('#yearOfGraduation').attr('placeholder', t.graduationYear);
        $('#currentEmployer').attr('placeholder', t.currentEmployer);
        $('#jobTitle').attr('placeholder', t.jobTitle);
        $('#currentSalary').attr('placeholder', t.currentSalary);
        $('#expectedSalary').attr('placeholder', t.expectedSalary);
        $('#reasonForLeaving').attr('placeholder', t.reasonForLeaving);
        
        // Update select options
        $('#department option[value=""]').text(t.selectDepartment);
        $('#position option[value=""]').text(t.selectPosition);
        $('#socialStatus option[value=""]').text(t.maritalStatus);
        $('#conscriptionStatus option[value=""]').text(t.militaryStatus);
        $('#englishLevel option[value=""]').text(t.englishLevel);
        $('#computerSkills option[value=""]').text(t.computerSkills);
        
        // Update religion dropdown
        const religionSelect = $('#religion');
        religionSelect.empty();
        religionSelect.append(`<option value="" disabled selected>${t.religion}</option>`);
        Object.entries(t.religionOptions).forEach(([key, value]) => {
            religionSelect.append(`<option value="${key}">${value}</option>`);
        });
        
        // Update buttons
        $('#prev').text(t.previous);
        $('#next').text(t.next);
        $('#submit').text(t.submit);
        
        // Update file upload label
        $('.custom-file-label').text(t.uploadCV);
        
        // Update modal text
        $('.modal-header h2').text(t.thankYou);
        $('#submitLoader p').text(t.submitting);
        $('.modal-body p').text(t.submitSuccess);
        
        // Update direction and alignment
        if (lang === 'ar') {
            // $('body').attr('dir', 'rtl').css('text-align', 'right');
            $('.input-container input, .input-container select').css('text-align', 'right');
        } else {
            // $('body').attr('dir', 'ltr').css('text-align', 'left');
            $('.input-container input, .input-container select').css('text-align', 'left');
        }
        
        // Update city dropdown
        const citySelect = $('#city');
        citySelect.empty(); // Clear existing options
        citySelect.append(`<option value="">${t.selectCity}</option>`);
        
        // Add city options
        Object.entries(t.cities).forEach(([key, value]) => {
            citySelect.append(`<option value="${key}">${value}</option>`);
        });
        
        // Update marital status dropdown
        const maritalSelect = $('#socialStatus');
        maritalSelect.empty();
        maritalSelect.append(`<option value="">${t.maritalStatus}</option>`);
        Object.entries(t.maritalStatusOptions).forEach(([key, value]) => {
            maritalSelect.append(`<option value="${key}">${value}</option>`);
        });
        
        // Update military status dropdown
        const militarySelect = $('#conscriptionStatus');
        militarySelect.empty();
        militarySelect.append(`<option value="">${t.militaryStatus}</option>`);
        Object.entries(t.militaryStatusOptions).forEach(([key, value]) => {
            militarySelect.append(`<option value="${key}">${value}</option>`);
        });
        
        // Update scientific degree dropdown
        const degreeSelect = $('#scientificDegree');
        degreeSelect.empty();
        degreeSelect.append(`<option value="">${t.scientificDegree}</option>`);
        Object.entries(t.gradeOptions).forEach(([key, value]) => {
            degreeSelect.append(`<option value="${key}">${value}</option>`);
        });
        
        // Update how did you hear about us dropdown
        const hearAboutSelect = $('#hearAboutUs');
        hearAboutSelect.empty();
        hearAboutSelect.append(`<option value="">${t.hearAboutUs}</option>`);
        Object.entries(t.hearAboutUsOptions).forEach(([key, value]) => {
            hearAboutSelect.append(`<option value="${key}">${value}</option>`);
        });
        
        // Update English level dropdown
        const englishLevelSelect = $('select[aria-label="مستوى اللغة الانجليزية"], select[aria-label="English Level"]');
        englishLevelSelect.attr('aria-label', t.englishLevel);
        englishLevelSelect.empty();
        englishLevelSelect.append(`<option value="">${t.englishLevel}</option>`);
        Object.entries(t.englishLevelOptions).forEach(([key, value]) => {
            englishLevelSelect.append(`<option value="${key}">${value}</option>`);
        });
        
        // Update computer skills dropdown
        const computerSkillsSelect = $('select[aria-label="مستوى مهارات الكمبيوتر"], select[aria-label="Computer Skills Level"]');
        computerSkillsSelect.attr('aria-label', t.computerSkills);
        computerSkillsSelect.empty();
        computerSkillsSelect.append(`<option value="">${t.computerSkills}</option>`);
        Object.entries(t.computerSkillsOptions).forEach(([key, value]) => {
            computerSkillsSelect.append(`<option value="${key}">${value}</option>`);
        });
        
        // Update chronic diseases dropdown
        const chronicDiseasesSelect = $('#chronicDiseases');
        chronicDiseasesSelect.empty();
        chronicDiseasesSelect.append(`<option value="" disabled selected>${t.chronicDiseases}</option>`);
        chronicDiseasesSelect.append(`<option value="yes">${t.yes}</option>`);
        chronicDiseasesSelect.append(`<option value="no">${t.no}</option>`);

        // Update relatives in hospital dropdown
        const relativesSelect = $('#relativeInHospital');
        relativesSelect.empty();
        relativesSelect.append(`<option value="" disabled selected>${t.relativesInHospital}</option>`);
        relativesSelect.append(`<option value="yes">${t.yes}</option>`);
        relativesSelect.append(`<option value="no">${t.no}</option>`);

        // Update work at Shifa dropdown
        const workAtShifaSelect = $('#workAtShifa');
        workAtShifaSelect.empty();
        workAtShifaSelect.append(`<option value="" disabled selected>${t.workAtShifa}</option>`);
        workAtShifaSelect.append(`<option value="yes">${t.yes}</option>`);
        workAtShifaSelect.append(`<option value="no">${t.no}</option>`);

        // Direct text replacement for the yes/no questions
        $('select').each(function() {
            const $select = $(this);
            
            if ($select.find('option:first').text() === 'Do you work at Shifa Hospital Before?') {
                $select.find('option:first').text(t.workAtShifa);
                $select.find('option[value="yes"]').text(t.yes);
                $select.find('option[value="no"]').text(t.no);
            }
            else if ($select.find('option:first').text() === 'Do you have any chronic diseases') {
                $select.find('option:first').text(t.chronicDiseases);
                $select.find('option[value="yes"]').text(t.yes);
                $select.find('option[value="no"]').text(t.no);
            }
            else if ($select.find('option:first').text() === 'Do you have Relatives in the Hospital') {
                $select.find('option:first').text(t.relativesInHospital);
                $select.find('option[value="yes"]').text(t.yes);
                $select.find('option[value="no"]').text(t.no);
            }
        });
        
        // Also update any visible labels or text
        $('div, label, span').each(function() {
            const $element = $(this);
            const text = $element.text().trim();
            
            switch(text) {
                case 'Do you work at Shifa Hospital Before?':
                    $element.text(t.workAtShifa);
                    break;
                case 'Do you have any chronic diseases':
                    $element.text(t.chronicDiseases);
                    break;
                case 'Do you have Relatives in the Hospital':
                    $element.text(t.relativesInHospital);
                    break;
            }
        });
    }

    // Language switch handler
    toggle.onclick = function() {
        if (flag.classList.contains('en')) {
            flag.classList.remove('en');
            flag.classList.add('ar');
            flag.src = "https://flagicons.lipis.dev/flags/4x3/eg.svg";
            $('body').attr('dir', 'rtl');
            updatePageLanguage('ar');
            // Safely destroy any existing Select2 instances with proper checking
            if ($.fn.select2) {
                // Get all elements that might have Select2
                const elements = $('.select2-search, #englishLevel, #computerSkills, #workAtShifa, #chronicDiseases, #relativeInHospital');
                
                // Only try to destroy if elements exist
                elements.each(function() {
                    const $el = $(this);
                    // Check if this specific element has Select2 initialized
                    if ($el.hasClass('select2-hidden-accessible') || $el.data('select2')) {
                        try {
                            $el.select2('destroy');
                        } catch (e) {
                            console.log('Select2 was not initialized on element:', $el.attr('id'));
                        }
                    }
                });
            }
            loadEducationalData();
        } else {
            flag.classList.remove('ar');
            flag.classList.add('en');
            flag.src = "https://flagicons.lipis.dev/flags/4x3/us.svg";
            $('body').attr('dir', 'ltr');
            updatePageLanguage('en');
            // Safely destroy any existing Select2 instances with proper checking
            if ($.fn.select2) {
                // Get all elements that might have Select2
                const elements = $('.select2-search, #englishLevel, #computerSkills, #workAtShifa, #chronicDiseases, #relativeInHospital');
                
                // Only try to destroy if elements exist
                elements.each(function() {
                    const $el = $(this);
                    // Check if this specific element has Select2 initialized
                    if ($el.hasClass('select2-hidden-accessible') || $el.data('select2')) {
                        try {
                            $el.select2('destroy');
                        } catch (e) {
                            console.log('Select2 was not initialized on element:', $el.attr('id'));
                        }
                    }
                });
            }
            loadEducationalData();
        }
    };

    console.log('Document ready fired');
    
    // Immediately check if elements exist
    const departmentSelect = document.getElementById('department');
    const positionSelect = document.getElementById('position');
    
    console.log('Department select:', departmentSelect);
    console.log('Position select:', positionSelect);

    // Keep only the validation initialization
    function updateValidationIcon(element) {
        const icon = element.nextElementSibling;
        if (element.value) {
            element.classList.add('is-valid');
            element.classList.remove('is-invalid');
        } else {
            element.classList.add('is-invalid');
            element.classList.remove('is-valid');
        }
    }

    // Initial validation check
    if (departmentSelect) updateValidationIcon(departmentSelect);
    if (positionSelect) updateValidationIcon(positionSelect);

    var base_color = "rgb(230,230,230)";
    var active_color = "#008D5C";

    // Define validateStep1Fields globally first
    function validateStep1Fields() {
        const requiredFields = {
            'firstName': document.querySelector('input[name="firstName"]'),
            'secondName': document.querySelector('input[name="secondName"]'),
            'thirdName': document.querySelector('input[name="thirdName"]'),
            'phoneNumber': document.getElementById('phoneNumber'),
            'nationalId': document.getElementById('nationalId'),
            'email': document.getElementById('email'),
            'address': document.getElementById('address'),
            'city': document.getElementById('city'),
            'socialStatus': document.getElementById('socialStatus'),
            'conscriptionStatus': document.getElementById('conscriptionStatus'),
            'department': document.getElementById('department'),
            'position': document.getElementById('position'),
            'expectedSalary': document.getElementById('expectedSalary')
        };

        let isValid = true;
        for (let field in requiredFields) {
            if (!requiredFields[field] || !requiredFields[field].value || 
                requiredFields[field].classList.contains('is-invalid')) {
                isValid = false;
                break;
            }
        }

        // Enable/disable the next button based on validation
        const nextButton = document.getElementById('next');
        if (nextButton) {
            if (!isValid) {
                nextButton.classList.add('disabled');
            } else {
                nextButton.classList.remove('disabled');
            }
        }
    }

    var child = 1;
    var length = $("section").length;
    $("#prev").hide();
    $("#submit").hide();

    $("section").not("section:nth-of-type(1)").hide();
    $("section").not("section:nth-of-type(1)").css('transform','translateX(100px)');

    var svgWidth = length * 200 + 24;
    $("#svg_wrap").html(
      '<svg version="1.1" id="svg_form_time" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 ' +
        svgWidth +
        ' 24" xml:space="preserve"></svg>'
    );

    function makeSVG(tag, attrs) {
      var el = document.createElementNS("http://www.w3.org/2000/svg", tag);
      for (var k in attrs) el.setAttribute(k, attrs[k]);
      return el;
    }

    for (i = 0; i < length; i++) {
      var positionX = 12 + i * 200;
      var rect = makeSVG("rect", { x: positionX, y: 9, width: 200, height: 6 });
      document.getElementById("svg_form_time").appendChild(rect);
      var circle = makeSVG("circle", {
        cx: positionX,
        cy: 12,
        r: 12,
        width: positionX,
        height: 6
      });
      document.getElementById("svg_form_time").appendChild(circle);
    }

    var circle = makeSVG("circle", {
      cx: positionX + 200,
      cy: 12,
      r: 12,
      width: positionX,
      height: 6
    });
    document.getElementById("svg_form_time").appendChild(circle);

    $('#svg_form_time rect').css('fill',base_color);
    $('#svg_form_time circle').css('fill',base_color);
    $("circle:nth-of-type(1)").css("fill", active_color);

    // Add this function to validate the current section's fields
    function validateCurrentSection() {
        const currentSection = $(`section:nth-of-type(${child})`);
        let isValid = true;

        // Check all required inputs and selects in current section
        currentSection.find('input[required], select[required]').each(function() {
            // Skip readonly fields and CV file input
            if ($(this).prop('readonly') || $(this).attr('id') === 'cvFile') return;

            // Check if field is empty or has invalid class
            if (!$(this).val() || $(this).hasClass('is-invalid')) {
                isValid = false;
                return false; // break the loop
            }
        });

        // Enable/disable the next button based on validation
        if (!isValid) {
            $('#next').addClass('disabled');
        } else {
            $('#next').removeClass('disabled');
        }
    }

    // Add validation check to all input/select change events
    $('section').on('input change', 'input, select', function() {
        validateCurrentSection();
    });

    // Update the existing button click handler
    $(".button").click(function () {
        var id = $(this).attr("id");
        if (id == "next") {
            // Check if button is disabled
            if ($(this).hasClass('disabled')) {
                // Show notification bar with error message
                $('#notificationBar')
                    .removeClass('success')
                    .addClass('error')
                    .css({
                        'display': 'block',
                        'top': '20px',
                        'left': '50%',
                        'transform': 'translateX(-50%)',
                        'z-index': '1000'
                    });
                $('#notificationMessage').text('Please fill all fields to continue');
                
                // Hide notification after 3 seconds
                setTimeout(function() {
                    $('#notificationBar').fadeOut();
                }, 3000);
                return;
            }

            $("#prev").removeClass("disabled");
            if (child >= length) {
                $(this).addClass("disabled");
                $('#submit').removeClass("disabled");
            }
            if (child <= length) {
                child++;
            }
        } else if (id == "prev") {
            $("#next").removeClass("disabled");
            $('#submit').addClass("disabled");
            if (child <= 2) {
                $(this).addClass("disabled");
            }
            if (child > 1) {
                child--;
            }
        }
        
        // Update SVG and section visibility as before
        var circle_child = child + 1;
        $("#svg_form_time rect").css("fill", base_color);
        $("#svg_form_time circle").css("fill", base_color);
        $("#svg_form_time rect:nth-of-type(-n+" + (child-1) + ")").css("fill", active_color);
        $("#svg_form_time circle:nth-of-type(-n+" + child + ")").css("fill", active_color);
        var currentSection = $("section:nth-of-type(" + child + ")");
        currentSection.fadeIn();
        currentSection.css('transform','translateX(0)');
        currentSection.prevAll('section').css('transform','translateX(-100px)');
        currentSection.nextAll('section').css('transform','translateX(100px)');
        $('section').not(currentSection).hide();
        updateButtonVisibility();

        // Validate the new current section after changing
        setTimeout(validateCurrentSection, 100);
    });

    // Call validateCurrentSection when the page loads
    $(document).ready(function() {
        validateCurrentSection();
        
        // Initialize ID type handling
        setupIdTypeHandling();
    });
    
    // ID Type handling
    function setupIdTypeHandling() {
        const idTypeSelect = document.getElementById('idType');
        const nationalIdInput = document.getElementById('nationalId');
        const dateOfBirthInput = document.getElementById('dateOfBirth');
        const governorateInput = document.getElementById('governorate');
        const genderInput = document.getElementById('gender');
        const ageInput = document.getElementById('age');
        const idTypeLabel = document.getElementById('idTypeLabel');
        const nationalIdError = document.getElementById('national-id-error');
        
        // Function to toggle field states based on ID type
        function toggleIdFields(isPassport) {
            if (isPassport) {
                // Passport mode
                nationalIdInput.placeholder = "Passport Number";
                idTypeLabel.textContent = "Passport Number";
                nationalIdError.textContent = "Please enter a valid passport number";
                
                // Enable manual editing of these fields
                dateOfBirthInput.readOnly = false;
                governorateInput.readOnly = false;
                genderInput.readOnly = false;
                
                // Change dateOfBirth to date input
                dateOfBirthInput.type = "date";
                dateOfBirthInput.addEventListener('change', updateAgeFromDateOfBirth);
                
                // Clear auto-extracted values if switching from National ID
                if (nationalIdInput.value && nationalIdInput.value.length === 14) {
                    dateOfBirthInput.value = '';
                    governorateInput.value = '';
                    genderInput.value = '';
                    ageInput.value = '';
                }
                
                // Remove validation constraints for national ID
                nationalIdInput.pattern = '.*';
                nationalIdInput.maxLength = 50;
            } else {
                // National ID mode
                nationalIdInput.placeholder = "National ID";
                idTypeLabel.textContent = "National ID";
                nationalIdError.textContent = "National ID must be exactly 14 digits";
                
                // Disable manual editing of these fields
                dateOfBirthInput.readOnly = true;
                governorateInput.readOnly = true;
                genderInput.readOnly = true;
                
                // Change dateOfBirth back to text
                dateOfBirthInput.type = "text";
                dateOfBirthInput.removeEventListener('change', updateAgeFromDateOfBirth);
                
                // Set validation constraints for National ID
                nationalIdInput.pattern = '\\d{14}';
                nationalIdInput.maxLength = 14;
            }
        }
        
        // Function to calculate age from selected birth date
        function updateAgeFromDateOfBirth() {
            const birthDateStr = dateOfBirthInput.value;
            if (birthDateStr) {
                try {
                    // Parse the date string based on input type
                    let birthDate;
                    
                    if (dateOfBirthInput.type === 'date') {
                        // For HTML date inputs (YYYY-MM-DD format)
                        birthDate = new Date(birthDateStr);
                    } else {
                        // For text inputs that might use different formats
                        // First try DD/MM/YYYY format
                        const parts = birthDateStr.split('/');
                        if (parts.length === 3) {
                            // Assuming DD/MM/YYYY format
                            const day = parseInt(parts[0], 10);
                            const month = parseInt(parts[1], 10) - 1; // Months are 0-based in JS
                            const year = parseInt(parts[2], 10);
                            birthDate = new Date(year, month, day);
                        } else {
                            // Fallback to standard parsing
                            birthDate = new Date(birthDateStr);
                        }
                    }
                    
                    // Check if date is valid
                    if (isNaN(birthDate.getTime())) {
                        console.error('Invalid date:', birthDateStr);
                        return;
                    }
                    
                    // Calculate age
                    const today = new Date();
                    let age = today.getFullYear() - birthDate.getFullYear();
                    
                    // Adjust age if birthday hasn't occurred yet this year
                    if (today.getMonth() < birthDate.getMonth() || 
                        (today.getMonth() === birthDate.getMonth() && today.getDate() < birthDate.getDate())) {
                        age--;
                    }
                    
                    // Update age field
                    ageInput.value = age;
                } catch (error) {
                    console.error('Error calculating age:', error);
                }
            }
        }
        
        // Initial setup based on default selection
        toggleIdFields(idTypeSelect.value === 'passport');
        
        // Handle ID type changes
        idTypeSelect.addEventListener('change', function() {
            const isPassport = this.value === 'passport';
            toggleIdFields(isPassport);
            
            // Clear the ID field when switching types
            nationalIdInput.value = '';
            nationalIdInput.classList.remove('is-valid', 'is-invalid');
            
            validateStep1Fields();
        });
    }

    // National ID validation
    document.getElementById('nationalId').addEventListener('input', function(e) {
        const idType = document.getElementById('idType').value;
        const idValue = e.target.value;
        
        // If it's a passport, simple validation
        if (idType === 'passport') {
            if (!idValue.trim()) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
            updateValidationIcon(this, false);
            } else {
                this.classList.add('is-valid');
                this.classList.remove('is-invalid');
                updateValidationIcon(this, true);
            }
            validateStep1Fields();
            return;
        }
        
        // National ID validation (14 digits)
        if (idValue.length !== 14) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
            updateValidationIcon(this, false);
            return;
        }
        
        // Special handling for passport-formatted national IDs (those starting with 9999)
        if (idValue.startsWith('9999')) {
            console.log('Detected passport-formatted National ID');
            
            // For passport-formatted IDs, don't attempt to extract birthdate/governorate
            this.classList.add('is-valid');
            this.classList.remove('is-invalid');
            updateValidationIcon(this, true);
            validateStep1Fields();
            return;
        }
        
        // Extract date components for standard national ID
        const year = idValue.substring(1, 3);
        const month = idValue.substring(3, 5);
        const day = idValue.substring(5, 7);
        const fullYear = parseInt(year) > 50 ? 1900 + parseInt(year) : 2000 + parseInt(year);
        const dateOfBirth = `${day}/${month}/${fullYear}`;
        document.getElementById('dateOfBirth').value = dateOfBirth;
        
        // Calculate age
        const birthDate = new Date(fullYear, parseInt(month) - 1, parseInt(day));
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        if (today.getMonth() < birthDate.getMonth() || 
            (today.getMonth() === birthDate.getMonth() && today.getDate() < birthDate.getDate())) {
            age--;
        }
        
        document.getElementById('age').value = age;
        this.style.color = '#28a745';
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
        updateValidationIcon(this, true);

        // Extract and set governorate
        const governorateCode = idValue.substring(7, 9);
        const governorates = {
            '01': 'Cairo',
            '02': 'Alexandria',
            '03': 'Port Said',
            '04': 'Suez',
            '11': 'Damietta',
            '12': 'Dakahlia',
            '13': 'Sharqia',
            '14': 'Qalyubia',
            '15': 'Kafr El Sheikh',
            '16': 'Gharbia',
            '17': 'Monufia',
            '18': 'Beheira',
            '19': 'Ismailia',
            '21': 'Giza',
            '22': 'Beni Suef',
            '23': 'Faiyum',
            '24': 'Minya',
            '25': 'Asyut',
            '26': 'Sohag',
            '27': 'Qena',
            '28': 'Aswan',
            '29': 'Luxor',
            '31': 'Red Sea',
            '32': 'New Valley',
            '33': 'Matrouh',
            '34': 'North Sinai',
            '35': 'South Sinai',
            '88': 'Outside Republic'
        };
        document.getElementById('governorate').value = governorates[governorateCode] || 'Unknown';
        
        // Set gender
        const genderDigit = parseInt(idValue.charAt(12));
        document.getElementById('gender').value = genderDigit % 2 === 0 ? 'Female' : 'Male';
        
        validateStep1Fields();
    });

    // Function to convert Hindi/Arabic numerals to English numerals
    function convertToEnglishNumbers(input) {
        const hindiNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return input.replace(/[٠-٩]/g, match => hindiNumerals.indexOf(match).toString());
    }
    
    // Phone validation with Hindi numeral support
    document.getElementById('phoneNumber').addEventListener('input', function(e) {
        let phone = e.target.value;
        // Convert any Hindi/Arabic numerals to English numerals for storage
        const convertedPhone = convertToEnglishNumbers(phone);
        
        const errorElement = document.getElementById('phone-error');
        const egyptianPhoneRegex = /^01[0125][0-9]{8}$/;
        
        // For validation, use the converted phone number
        if (!egyptianPhoneRegex.test(convertedPhone)) {
            this.style.color = 'red';
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
            errorElement.style.display = 'block';
        } else {
            this.style.color = '#28a745';
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
            errorElement.style.display = 'none';
            
            // Store the converted value for submission to database
            this.dataset.convertedValue = convertedPhone;
        }
        validateStep1Fields();
    });

    // Email validation
    document.getElementById('email').addEventListener('input', function(e) {
        const email = e.target.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            this.style.color = 'red';
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.style.color = '#28a745';
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
        validateStep1Fields();
    });

    // Name validation
    const nameInputs = document.querySelectorAll('input[name^="firstName"], input[name^="secondName"], input[name^="thirdName"]');
    nameInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            const value = e.target.value;
            const arabicPattern = /^[\u0600-\u06FF]+$/;  // Arabic characters only
            const englishPattern = /^[A-Za-z ]+$/;      // English characters only
            
            if (value === '') {
                this.classList.remove('is-valid', 'is-invalid');
                document.getElementById('name-error').style.display = 'none';
            } else if (arabicPattern.test(value) || englishPattern.test(value)) {
                this.classList.add('is-valid');
                this.classList.remove('is-invalid');
                document.getElementById('name-error').style.display = 'none';
            } else {
                this.classList.add('is-invalid');
                this.classList.remove('is-valid');
                document.getElementById('name-error').style.display = 'block';
            }
            validateStep1Fields();
        });
    });

    // Address validation
    document.getElementById('address').addEventListener('input', function(e) {
        const address = e.target.value.trim();
        if (address.length < 10) {
            this.style.color = 'red';
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.style.color = '#28a745';
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
        validateStep1Fields();
    });

    // City validation
    document.getElementById('city').addEventListener('change', function(e) {
        if (!this.value) {
            this.style.color = 'red';
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.style.color = '#28a745';
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
        validateStep1Fields();
    });

    // Military Status validation
    document.getElementById('conscriptionStatus').addEventListener('change', function(e) {
        if (!this.value) {
            this.style.color = 'red';
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.style.color = '#28a745';
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
        validateStep1Fields();
    });

    // Marital Status validation
    document.getElementById('socialStatus').addEventListener('change', function(e) {
        if (!this.value) {
            this.style.color = 'red';
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.style.color = '#28a745';
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
        validateStep1Fields();
    });

    // Add validation for department and position
    document.getElementById('department').addEventListener('change', function(e) {
        const departmentId = this.value;
        const positionSelect = document.getElementById('position');
        
        // Enable the position select
        positionSelect.disabled = false;
        
        // Hide all options first
        Array.from(positionSelect.options).forEach(option => {
            if (option.dataset.department === departmentId) {
                option.style.display = '';
            } else {
                option.style.display = 'none';
            }
        });
        
        // Reset position selection
        positionSelect.value = '';
    });

    document.getElementById('position').addEventListener('change', function(e) {
        if (!this.value) {
            this.style.color = 'red';
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.style.color = '#28a745';
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
        validateStep1Fields();
    });

    // Add salary validation
    document.getElementById('expectedSalary').addEventListener('input', function(e) {
        const salary = e.target.value.trim();
        const errorElement = document.getElementById('expected-salary-error');
        // Regex to match numbers only, allowing decimals
        const salaryRegex = /^\d+(\.\d{0,2})?$/;
        
        if (!salaryRegex.test(salary) || parseFloat(salary) <= 0) {
            this.style.color = 'red';
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
            errorElement.style.display = 'block';
            errorElement.textContent = 'Please enter a valid salary amount (numbers only)';
        } else {
            this.style.color = '#28a745';
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
            errorElement.style.display = 'none';
        }
        validateStep1Fields();
    });

    // Add input event listener to format the salary as currency
    document.getElementById('expectedSalary').addEventListener('blur', function(e) {
        if (this.classList.contains('is-valid')) {
            const value = parseFloat(this.value);
            if (!isNaN(value)) {
                // Format as currency with 2 decimal places
                this.value = value.toFixed(2);
            }
        }
    });

    function updateValidationIcon(element, isValid) {
        const iconElement = element.nextElementSibling;
        if (iconElement) {
            iconElement.className = 'validation-icon ' + (isValid ? 'success' : 'error');
        }
    }

    // Initial validation check
    validateStep1Fields();

    // Add this new function to update button visibility based on current section
    function updateButtonVisibility() {
        if (child === 1) {
            // First page - show only Next button
            $("#prev").hide();
            $("#next").show();
            $("#submit").hide();
        } else if (child === length) {
            // Last page - show Previous and Submit buttons
            $("#prev").show();
            $("#next").hide();
            $("#submit").show();
        } else {
            // Middle pages - show Previous and Next buttons
            $("#prev").show();
            $("#next").show();
            $("#submit").hide();
        }
    }

    // Call this initially to set up correct button visibility
    updateButtonVisibility();

    // Initialize language on page load
    $(document).ready(function() {
        // Set Arabic as default language
        const flag = document.getElementById("flag");
        flag.classList.remove('en');
        flag.classList.add('ar');
        flag.src = "https://flagicons.lipis.dev/flags/4x3/eg.svg";
        $('body').attr('dir', 'rtl');
        updatePageLanguage('ar');
        
        // Safely destroy any existing Select2 instances
        safelyDestroySelect2();
        
        loadEducationalData();
    });

    // Initialize Select2 for educational dropdowns
    function initializeSelect2Dropdowns() {
        const t = getCurrentTranslation();
        const isRtl = isArabic();
        const commonConfig = {
            dir: isRtl ? 'rtl' : 'ltr',
            language: isRtl ? 'ar' : 'en',
            width: '100%',
            placeholder: {
                id: '-1',
                text: isRtl ? 'اختر...' : 'Select...'
            },
            allowClear: true
        };

        // Initialize Specialization dropdown
        $('#specialization').select2({
            ...commonConfig,
            placeholder: {
                id: '-1',
                text: t.selectSpecialization
            }
        });

        // Initialize University dropdown
        $('#university').select2({
            ...commonConfig,
            placeholder: {
                id: '-1',
                text: t.selectUniversity
            }
        });

        // Initialize Faculty dropdown
        $('#faculty').select2({
            ...commonConfig,
            placeholder: {
                id: '-1',
                text: t.selectFaculty
            }
        });

        // Initialize Grade dropdown
        $('#grade').select2({
            ...commonConfig,
            placeholder: {
                id: '-1',
                text: t.selectGrade
            }
        });

        // Handle RTL/LTR changes
        if (isRtl) {
            $('.select2-container--default .select2-selection--single').css('text-align', 'right');
        } else {
            $('.select2-container--default .select2-selection--single').css('text-align', 'left');
        }
    }

    // Initialize Other Information dropdowns with Select2
    function initializeOtherInfoDropdowns() {
        const t = getCurrentTranslation();
        const isRtl = isArabic();
        const commonConfig = {
            dir: isRtl ? 'rtl' : 'ltr',
            language: isRtl ? 'ar' : 'en',
            width: '100%',
            allowClear: true
        };

        // Initialize English Level dropdown
        $('#englishLevel').select2({
            ...commonConfig,
            placeholder: {
                id: '-1',
                text: t.englishLevel
            }
        });

        // Initialize Computer Skills dropdown
        $('#computerSkills').select2({
            ...commonConfig,
            placeholder: {
                id: '-1',
                text: t.computerSkills
            }
        });

        // Initialize Work at Shifa dropdown
        $('#workAtShifa').select2({
            ...commonConfig,
            placeholder: {
                id: '-1',
                text: t.workAtShifa
            },
            minimumResultsForSearch: Infinity // Disable search for yes/no dropdowns
        }).empty()
            .append(`<option value="">${t.workAtShifa}</option>`)
            .append(`<option value="yes">${t.yes}</option>`)
            .append(`<option value="no">${t.no}</option>`);

        // Initialize Chronic Diseases dropdown
        $('#chronicDiseases').select2({
            ...commonConfig,
            placeholder: {
                id: '-1',
                text: t.chronicDiseases
            },
            minimumResultsForSearch: Infinity // Disable search for yes/no dropdowns
        }).empty()
            .append(`<option value="">${t.chronicDiseases}</option>`)
            .append(`<option value="yes">${t.yes}</option>`)
            .append(`<option value="no">${t.no}</option>`);

        // Initialize Relatives in Hospital dropdown
        $('#relativeInHospital').select2({
            ...commonConfig,
            placeholder: {
                id: '-1',
                text: t.relativesInHospital
            },
            minimumResultsForSearch: Infinity // Disable search for yes/no dropdowns
        }).empty()
            .append(`<option value="">${t.relativesInHospital}</option>`)
            .append(`<option value="yes">${t.yes}</option>`)
            .append(`<option value="no">${t.no}</option>`);

        // Handle RTL/LTR text alignment
        if (isRtl) {
            $('.select2-container--default .select2-selection--single').css('text-align', 'right');
        } else {
            $('.select2-container--default .select2-selection--single').css('text-align', 'left');
        }
    }

    // Update loadEducationalData function to also initialize other info dropdowns
    async function loadEducationalData() {
        try {
            // Fetch qualifications
            const qualificationsResponse = await fetch('/api/qualifications');
            const qualifications = await qualificationsResponse.json();
            
            // Fetch universities
            const universitiesResponse = await fetch('/api/universities');
            const universities = await universitiesResponse.json();
            
            // Fetch faculties
            const facultiesResponse = await fetch('/api/faculties');
            const faculties = await facultiesResponse.json();

            const isAr = $('#flag').hasClass('ar'); // Check if Arabic is selected

            // Populate specialization dropdown
            const specializationSelect = $('#specialization');
            specializationSelect.empty();
            specializationSelect.append(`<option value="" disabled selected>${isAr ? 'اختر التخصص' : 'Select Specialization'}</option>`);
            qualifications.forEach(qual => {
                const name = isAr ? qual.GradeName_AR : qual.GradeName_EN;
                specializationSelect.append(`<option value="${qual.Grade_ID}" 
                    data-category-ar="${qual.CategoryName_AR}" 
                    data-category-en="${qual.CategoryName_EN}"
                    data-category-id="${qual.Category_ID}"
                    data-grade-id="${qual.Grade_ID}"
                    data-grade-ar="${qual.GradeName_AR}"
                    data-grade-en="${qual.GradeName_EN}">${name}</option>`);
            });

            // Populate university dropdown
            const universitySelect = $('#university');
            universitySelect.empty();
            universitySelect.append(`<option value="" disabled selected>${isAr ? 'اختر الجامعة' : 'Select University'}</option>`);
            universities.forEach(uni => {
                const name = isAr ? uni.uniname_ar : uni.uniname_en;
                universitySelect.append(`<option value="${uni.uni_id}">${name}</option>`);
            });

            // Populate faculty dropdown
            const facultySelect = $('#faculty');
            facultySelect.empty();
            facultySelect.append(`<option value="" disabled selected>${isAr ? 'اختر الكلية' : 'Select Faculty'}</option>`);
            faculties.forEach(fac => {
                const name = isAr ? fac.facname_ar : fac.facname_en;
                facultySelect.append(`<option value="${fac.fac_id}">${name}</option>`);
            });

            // Update grade dropdown
            const gradeSelect = $('#grade');
            gradeSelect.empty();
            gradeSelect.append(`<option value="" disabled selected>${isAr ? 'اختر التقدير' : 'Select Grade'}</option>`);
            const gradeOptions = isAr ? {
                'Excellent': 'ممتاز',
                'Very Good': 'جيد جداً',
                'Good': 'جيد',
                'Pass': 'مقبول'
            } : {
                'Excellent': 'Excellent',
                'Very Good': 'Very Good',
                'Good': 'Good',
                'Pass': 'Pass'
            };
            Object.entries(gradeOptions).forEach(([value, text]) => {
                gradeSelect.append(`<option value="${value}">${text}</option>`);
            });

            // Initialize Select2 after populating dropdowns
            initializeSelect2Dropdowns();
            initializeOtherInfoDropdowns();

            // Update scientific degree based on selected specialization
            const selectedSpecialization = specializationSelect.val();
            if (selectedSpecialization) {
                const selectedOption = specializationSelect.find('option:selected');
                const categoryName = isAr ? 
                    selectedOption.data('category-ar') : 
                    selectedOption.data('category-en');
                $('#scientificDegree').val(categoryName);
            }

        } catch (error) {
            console.error('Error loading educational data:', error);
        }
    }

    // Update the specialization change handler
    $('#specialization').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const isAr = $('#flag').hasClass('ar');
        const categoryName = isAr ? 
            selectedOption.data('category-ar') : 
            selectedOption.data('category-en');
        $('#scientificDegree').val(categoryName);
        validateEducationalFields();
    });

    // Function to check if current language is Arabic
    function isArabic() {
        return $('#flag').hasClass('ar');
    }

    // Function to get current translations
    function getCurrentTranslation() {
        return translations[isArabic() ? 'ar' : 'en'];
    }

    // Validate educational fields
    function validateEducationalFields() {
        // Only specialization, scientificDegree and yearOfGraduation are required
        const requiredFields = ['specialization', 'scientificDegree', 'yearOfGraduation'];
        // Optional fields that should be validated if they have a value
        const optionalFields = ['university', 'faculty', 'grade'];
        
        let isValid = true;

        // Validate required fields
        requiredFields.forEach(field => {
            const element = document.getElementById(field);
            if (!element.value) {
                isValid = false;
                element.classList.add('is-invalid');
                element.classList.remove('is-valid');
            } else {
                element.classList.add('is-valid');
                element.classList.remove('is-invalid');
            }
        });

        // Validate optional fields only if they have a value
        optionalFields.forEach(field => {
            const element = document.getElementById(field);
            if (element.value) {
                element.classList.add('is-valid');
                element.classList.remove('is-invalid');
            } else {
                // For optional fields, neither valid nor invalid if empty
                element.classList.remove('is-valid');
                element.classList.remove('is-invalid');
            }
        });

        return isValid;
    }

    // Load educational data when page loads
    loadEducationalData();

    // Update educational fields when language changes
    toggle.addEventListener('click', function() {
        const flag = document.getElementById("flag");
        // Toggle between Arabic and English
        if (flag.classList.contains('ar')) {
            // Switch to English
            flag.classList.remove('ar');
            flag.classList.add('en');
            flag.src = "https://flagicons.lipis.dev/flags/4x3/us.svg";
            $('body').attr('dir', 'ltr');
            updatePageLanguage('en');
            // Safely destroy Select2 instances
            safelyDestroySelect2();
        } else {
            // Switch to Arabic
            flag.classList.remove('en');
            flag.classList.add('ar');
            flag.src = "https://flagicons.lipis.dev/flags/4x3/eg.svg";
            $('body').attr('dir', 'rtl');
            updatePageLanguage('ar');
            // Safely destroy Select2 instances
            safelyDestroySelect2();
        }
        setTimeout(loadEducationalData, 100);
    });

    // Add validation for graduation year
    $('#yearOfGraduation').on('input', function() {
        const year = parseInt(this.value);
        const currentYear = new Date().getFullYear();
        
        if (year < 1950 || year > currentYear || !this.value) {
            this.classList.add('is-invalid');
            this.classList.remove('is-valid');
            $('#graduation-error').show();
        } else {
            this.classList.add('is-valid');
            this.classList.remove('is-invalid');
            $('#graduation-error').hide();
        }
        validateEducationalFields();
    });

    // Add validation for all educational fields
    $('#specialization, #university, #faculty, #grade').on('change', validateEducationalFields);

    // Function to handle final form submission
    async function submitForm() {
        try {
            // Show loading indicator
            $("#submitBtn").prop('disabled', true);
            $("#submitText").hide();
            $("#submitSpinner").show();

            // Collect form data
            const formData = collectFormData();

            // Check if we have a token (URL parameter)
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            const isTokenUpdate = !!token;

            if (isTokenUpdate) {
                console.log('Processing as token update with token:', token);
            }

            // Log the data being sent
            console.log('Submitting form data:', formData);

            // Submit the form data via AJAX
            const response = await fetch('/api/save-candidate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();

            // Check if the submission was successful
            if (result.success) {
                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: result.message,
                    confirmButtonColor: '#008D5C'
                }).then(() => {
                    // Redirect to home page or show completion message
                    if (isTokenUpdate) {
                        // For token updates, show a special message indicating the token was used
                        Swal.fire({
                            icon: 'info',
                            title: 'Application Updated',
                            text: 'Your application has been updated successfully. This link is now expired.',
                            confirmButtonColor: '#008D5C'
                        }).then(() => {
                            // Redirect to home page without the token parameter
                            window.location.href = window.location.pathname;
                        });
                    } else {
                        // For regular submissions, just redirect
                        window.location.href = '/';
                    }
                });
            } else {
                // Show error message
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: result.error || 'An error occurred while submitting the form',
                    confirmButtonColor: '#008D5C'
                });
                
                // Re-enable the submit button
                $("#submitBtn").prop('disabled', false);
                $("#submitText").show();
                $("#submitSpinner").hide();
            }
        } catch (error) {
            console.error('Error submitting form:', error);
            // Show error message
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'An error occurred while submitting the form',
                confirmButtonColor: '#008D5C'
            });
            
            // Re-enable the submit button
            $("#submitBtn").prop('disabled', false);
            $("#submitText").show();
            $("#submitSpinner").hide();
        }
    }

    // Add new function for form recovery on page load
    function checkForFormRecovery() {
        const hasBackup = sessionStorage.getItem('formBackup');
        if (hasBackup) {
            Swal.fire({
                title: 'Form Data Recovery',
                text: 'We detected previously entered data. Would you like to recover it?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Yes, recover data',
                cancelButtonText: 'No, start over'
            }).then((result) => {
                if (result.isConfirmed) {
                    const backupData = JSON.parse(sessionStorage.getItem('formBackup'));
                    prefillFormWithCandidateData(backupData);
                } else {
                    sessionStorage.removeItem('formBackup');
                }
            });
        }
    }

    // Add responsive design check function
    function checkResponsiveCompatibility() {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        
        if (isMobile) {
            console.log('Mobile device detected:', navigator.userAgent);
            
            // Check for potential issues on this device
            if (screenWidth < 360) {
                showWarningNotification('Your screen may be too small for optimal form experience. Please consider using a device with a larger screen.');
            }
            
            // Add mobile-specific classes to body
            document.body.classList.add('mobile-device');
            
            // Adjust form controls for better mobile experience
            $('.form-control, .select2-container').addClass('mobile-input');
            
            // Fix date inputs on iOS
            if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                $('.date-input').attr('type', 'date');
            }
        }
    }

    // Show warning notification
    function showWarningNotification(message) {
        Swal.fire({
            title: 'Warning',
            text: message,
            icon: 'warning',
            confirmButtonText: 'Understood'
        });
    }

    // Initialize on document ready
    $(document).ready(function() {
        // ... existing code ...
        
        // Check for device compatibility
        checkResponsiveCompatibility();
        
        // Check for form recovery
        checkForFormRecovery();
    });

    // ... existing code ...

    // Add network connectivity detection
    function setupNetworkDetection() {
        // Add offline indicator to the page
        const offlineIndicator = document.createElement('div');
        offlineIndicator.className = 'offline-indicator';
        offlineIndicator.textContent = 'You are currently offline. Please reconnect to submit your application.';
        document.body.appendChild(offlineIndicator);
        
        // Check initial state
        if (!navigator.onLine) {
            document.body.classList.add('is-offline');
        }
        
        // Listen for online/offline events
        window.addEventListener('online', function() {
            document.body.classList.remove('is-offline');
            console.log('Connection restored');
            
            // Check if there was a failed submission when offline
            if (sessionStorage.getItem('pendingSubmission')) {
                Swal.fire({
                    title: 'Connection Restored',
                    text: 'Would you like to attempt to submit your application now?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, submit now',
                    cancelButtonText: 'No, I\'ll do it later'
                }).then((result) => {
                    if (result.isConfirmed) {
                        submitForm();
                    }
                });
            }
        });
        
        window.addEventListener('offline', function() {
            document.body.classList.add('is-offline');
            console.log('Connection lost');
            
            // Show notification if user is filling out the form
            if (document.activeElement && 
                (document.activeElement.tagName === 'INPUT' || 
                 document.activeElement.tagName === 'SELECT' || 
                 document.activeElement.tagName === 'TEXTAREA')) {
                
                Swal.fire({
                    title: 'Connection Lost',
                    text: 'You appear to be offline. Your form data will be saved locally, but you\'ll need an internet connection to submit.',
                    icon: 'warning',
                    confirmButtonText: 'Got it'
                });
            }
        });
    }

    // Modify the collectFormData function to ensure all input is properly sanitized
    function collectFormData() {
        const formData = {};
        
        // Get all form inputs
        const inputs = document.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            if (input.id && input.id !== '') {
                // Basic sanitization - trim whitespace and prevent empty strings
                let value = input.value.trim();
                formData[input.id] = value !== '' ? value : null;
            }
        });
        
        // Format the data for backend
        formData.Name = `${formData.firstName} ${formData.secondName} ${formData.thirdName}`.trim();
        formData.Mobile = formData.phoneNumber;
        formData.Current_Address = formData.address;
        formData.Email = formData.email;
        formData.Age = parseInt(formData.age) || 0;
        formData.Military_Status = formData.conscriptionStatus;
        formData.Marital_Status = formData.socialStatus;
        formData.Religion = formData.religion;
        formData.Current_Employer = formData.currentEmployer;
        formData.Job_Title = formData.jobTitle;
        
        // Ensure salary values are valid numbers
        formData.Current_Salary = formData.currentSalary ? parseFloat(formData.currentSalary.replace(/[^\d.-]/g, '')) || 0 : 0;
        formData.Leave_Reason = formData.reasonForLeaving;
        formData.EXP_Years_From = formData.from;
        formData.EXP_Years_To = formData.to;
        formData.Expected_Salary = formData.expectedSalary ? parseFloat(formData.expectedSalary.replace(/[^\d.-]/g, '')) || 0 : 0;
        formData.Grad_Year = formData.yearOfGraduation;
        formData.Grade = formData.grade;
        formData.EMP_ENG_LVL = formData.englishLevel;
        formData.EMP_PC_LVL = formData.computerSkills;
        formData.Has_Relative_In_Hospital = formData.relativeInHospital;
        formData.Worked_At_Shifa_Before = formData.workAtShifa;
        formData.Has_Chronic_Disease = formData.chronicDiseases;
        formData.Gender = formData.gender;
        formData.Hear_About_Us = formData.hearAboutUs;
        
        // Handle ID Type
        formData.ID_Type = formData.idType || 'nationalId';
        
        if (formData.ID_Type === 'passport') {
            // Store original passport number
            formData.Passport_Number = formData.nationalId;
            
            // Create numeric version for National_ID field (must pass CHECK constraint)
            // Format: 9999 followed by padded digits to reach 14 digits total
            let numericPassport = '';
            
            // Start with prefix 9999 to indicate passport
            numericPassport = '9999';
            
            // Take up to 10 digits from the passport, or pad with zeros
            const passportDigits = formData.nationalId.replace(/\D/g, ''); // Remove non-digits
            const paddedPassportDigits = (passportDigits + '0000000000').substring(0, 10);
            
            // Create the 14-digit National_ID
            formData.National_ID = numericPassport + paddedPassportDigits;
            
            console.log('Formatted passport for DB:', formData.National_ID);
        } else {
            // Regular National ID processing
            formData.National_ID = formData.nationalId;
            formData.Passport_Number = null;
        }
        
        // Add other necessary fields from the form
        formData.Main_Position = parseInt(document.getElementById('departmentId').value) || 0;
        formData.SECTIONId = parseInt(document.getElementById('sectionId').value) || 0;
        
        // Use the position text value instead of ID for Sub_Position
        const positionElement = document.getElementById('position');
        if (positionElement) {
            // Get the text value from the selected option
            if (positionElement.selectedIndex >= 0) {
                formData.Sub_Position = positionElement.options[positionElement.selectedIndex].text;
            } else {
                formData.Sub_Position = positionElement.value; // Fallback to value if no option is selected
            }
        }
        
        // Add qualification data
        if (document.getElementById('specialization')) {
            formData.Category_ID = parseInt(document.getElementById('specialization').value) || 0;
            formData.CategoryName_AR = $("#specialization option:selected").text();
        }
        
        if (document.getElementById('faculty')) {
            formData.FID = parseInt(document.getElementById('faculty').value) || 0;
            formData.Faculty = $("#faculty option:selected").text();
        }
        
        if (document.getElementById('university')) {
            // Get the university name
            formData.uniname_ar = $("#university option:selected").text();
            
            // Get the university ID and set it as UID
            formData.UID = parseInt(document.getElementById('university').value) || 0;
            console.log('Setting UID from university selection:', formData.UID);
        }
        
        if (document.getElementById('scientificDegree')) {
            formData.Grade_ID = 1; // Default, can be updated if available
            formData.GradeName_AR = document.getElementById('scientificDegree').value;
        }
        
        return formData;
    }

    // Modify the submitForm function to check network status
    async function submitForm() {
        // Check network status first
        if (!navigator.onLine) {
            // Save the form data for later submission
            const formData = collectFormData();
            sessionStorage.setItem('formBackup', JSON.stringify(formData));
            sessionStorage.setItem('pendingSubmission', 'true');
            
            Swal.fire({
                title: 'No Connection',
                text: 'You are currently offline. Your form data has been saved and you can submit when you regain connection.',
                icon: 'warning',
                confirmButtonText: 'Understood'
            });
            
            return;
        }
        
        // Clear pending submission flag
        sessionStorage.removeItem('pendingSubmission');
        
        // Continue with the regular submit process
        try {
            // ... existing code ...
        } catch (error) {
            // ... existing error handling ...
        }
    }

    // Initialize on document ready
    $(document).ready(function() {
        // ... existing code ...
        
        // Setup network detection
        setupNetworkDetection();
    });

    // Add the missing function
    function safelyDestroySelect2() {
        if ($.fn.select2) {
            // Get all elements that might have Select2
            const elements = $('.select2-search, #englishLevel, #computerSkills, #workAtShifa, #chronicDiseases, #relativeInHospital');
            
            // Only try to destroy if elements exist
            elements.each(function() {
                const $el = $(this);
                // Check if this specific element has Select2 initialized
                if ($el.hasClass('select2-hidden-accessible') || $el.data('select2')) {
                    try {
                        $el.select2('destroy');
                    } catch (e) {
                        console.log('Select2 was not initialized on element:', $el.attr('id'));
                    }
                }
            });
        }
    }

    // Add a function to show warning messages
    function showWarningMessage(message) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'warning',
                title: 'Please Wait',
                text: message,
                confirmButtonColor: '#008D5C'
            });
        } else {
            alert(message);
        }
    }

    // Update ID Type dropdown
    function updateIdTypeOptions() {
        const lang = $('body').attr('dir') === 'rtl' ? 'ar' : 'en';
        const t = translations[lang];
        
        const idTypeSelect = $('#idType');
        idTypeSelect.find('option[value="nationalId"]').text(t.idTypeOptions.nationalId);
        idTypeSelect.find('option[value="passport"]').text(t.idTypeOptions.passport);
        
        // Also update the nationalId label based on current selection
        const idType = idTypeSelect.val();
        $('#idTypeLabel').text(idType === 'passport' ? t.passportNumber : t.nationalId);
        $('#nationalId').attr('placeholder', idType === 'passport' ? t.passportNumber : t.nationalId);
    }
    
    // Update when language changes
    toggle.addEventListener("click", function() {
        const isArabic = flag.classList.contains('en');
        
        if(isArabic) {
            flag.classList.remove('en');
            flag.classList.add('ar');
            flag.src = "https://flagicons.lipis.dev/flags/4x3/eg.svg";
            document.body.setAttribute('dir', 'rtl');
            updatePageLanguage('ar');
        } else {
            flag.classList.remove('ar');
            flag.classList.add('en');
            flag.src = "https://flagicons.lipis.dev/flags/4x3/us.svg";
            document.body.setAttribute('dir', 'ltr');
            updatePageLanguage('en');
        }
        
        // Update ID Type options after language change
        updateIdTypeOptions();
    });
    
    // Also update when ID Type changes
    $('#idType').on('change', function() {
        updateIdTypeOptions();
    });
});