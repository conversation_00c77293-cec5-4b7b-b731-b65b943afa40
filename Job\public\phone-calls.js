class PhoneCallManager {
    constructor(candidateId) {
        this.candidateId = candidateId;
        this.calls = [];
        this.initialized = false;
    }

    async init() {
        if (this.initialized) return;
        
        try {
            this.createUI();
            await this.loadPhoneCalls();
            this.setupEventListeners();
            this.initialized = true;
        } catch (error) {
            console.error('Failed to initialize PhoneCallManager:', error);
        }
    }

    createUI() {
        // Create a section for phone calls in the candidate profile
        const container = document.createElement('div');
        container.className = 'phone-calls-container';
        container.innerHTML = `
            <div class="phone-calls-header">
                <h3>Phone Call Log</h3>
                <button id="add-call-btn" class="btn btn-primary btn-sm">
                    <i class="fas fa-phone-alt"></i> Add Call
                </button>
            </div>
            <div class="phone-calls-list">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Caller</th>
                            <th>Date & Time</th>
                            <th>Duration</th>
                            <th>Status</th>
                            <th>Notes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="phone-calls-tbody">
                        <tr class="no-calls-row">
                            <td colspan="6" class="text-center">No phone calls recorded yet</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="call-form-container" style="display:none;">
                <form id="call-form">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="caller-name">Caller Name</label>
                            <input type="text" class="form-control" id="caller-name" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="call-datetime">Date & Time</label>
                            <input type="datetime-local" class="form-control" id="call-datetime" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="call-duration">Duration (minutes)</label>
                            <input type="number" class="form-control" id="call-duration" min="0" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="call-status">Status</label>
                            <select class="form-control" id="call-status" required>
                                <option value="Completed">Completed</option>
                                <option value="Missed">Missed</option>
                                <option value="Scheduled">Scheduled</option>
                                <option value="Cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="call-notes">Notes</label>
                        <textarea class="form-control" id="call-notes" rows="3"></textarea>
                    </div>
                    <div class="form-buttons">
                        <button type="submit" class="btn btn-success">Save Call</button>
                        <button type="button" id="cancel-call-btn" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        `;
        
        // Find a suitable place to append this container
        const candidateSection = document.querySelector('.candidate-profile') || 
                                document.querySelector('#candidate-form') ||
                                document.querySelector('main') ||
                                document.body;
        
        candidateSection.appendChild(container);
        
        // Add some basic styling
        const style = document.createElement('style');
        style.textContent = `
            .phone-calls-container {
                margin-top: 20px;
                margin-bottom: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
                background-color: #f9f9f9;
            }
            .phone-calls-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }
            .call-form-container {
                margin-top: 20px;
                padding: 15px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #fff;
            }
            .form-buttons {
                margin-top: 15px;
                text-align: right;
            }
            .call-actions {
                display: flex;
                gap: 5px;
                justify-content: center;
            }
            .no-calls-row {
                font-style: italic;
                color: #777;
            }
        `;
        document.head.appendChild(style);
    }

    async loadPhoneCalls() {
        try {
            const response = await fetch(`/api/phone-calls/${this.candidateId}`);
            if (!response.ok) {
                throw new Error(`Error fetching phone calls: ${response.statusText}`);
            }
            
            const result = await response.json();
            this.calls = result.data || [];
            this.renderPhoneCalls();
        } catch (error) {
            console.error('Failed to load phone calls:', error);
        }
    }

    renderPhoneCalls() {
        const tbody = document.getElementById('phone-calls-tbody');
        if (!tbody) return;
        
        tbody.innerHTML = '';
        
        if (this.calls.length === 0) {
            tbody.innerHTML = `
                <tr class="no-calls-row">
                    <td colspan="6" class="text-center">No phone calls recorded yet</td>
                </tr>
            `;
            return;
        }
        
        this.calls.forEach(call => {
            const row = document.createElement('tr');
            
            // Format date for display
            const callDate = new Date(call.CallDateTime);
            const formattedDate = callDate.toLocaleDateString() + ' ' + callDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            row.innerHTML = `
                <td>${this.escapeHtml(call.CallerName)}</td>
                <td>${formattedDate}</td>
                <td>${call.CallDuration} min</td>
                <td><span class="badge badge-${this.getStatusBadgeClass(call.CallStatus)}">${call.CallStatus}</span></td>
                <td>${this.escapeHtml(call.CallNotes || '')}</td>
                <td class="call-actions">
                    <button class="btn btn-sm btn-info edit-call" data-call-id="${call.CallId}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-call" data-call-id="${call.CallId}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }

    getStatusBadgeClass(status) {
        switch (status) {
            case 'Completed': return 'success';
            case 'Missed': return 'danger';
            case 'Scheduled': return 'primary';
            case 'Cancelled': return 'warning';
            default: return 'secondary';
        }
    }

    escapeHtml(unsafe) {
        if (!unsafe) return '';
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    setupEventListeners() {
        // Add call button
        const addCallBtn = document.getElementById('add-call-btn');
        if (addCallBtn) {
            addCallBtn.addEventListener('click', () => this.showCallForm());
        }
        
        // Cancel button
        const cancelCallBtn = document.getElementById('cancel-call-btn');
        if (cancelCallBtn) {
            cancelCallBtn.addEventListener('click', () => this.hideCallForm());
        }
        
        // Form submission
        const callForm = document.getElementById('call-form');
        if (callForm) {
            callForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveCall();
            });
        }
        
        // Edit and delete buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.edit-call')) {
                const button = e.target.closest('.edit-call');
                const callId = button.dataset.callId;
                this.editCall(callId);
            } else if (e.target.closest('.delete-call')) {
                const button = e.target.closest('.delete-call');
                const callId = button.dataset.callId;
                this.deleteCall(callId);
            }
        });
    }

    showCallForm(callData = null) {
        const formContainer = document.querySelector('.call-form-container');
        if (!formContainer) return;
        
        formContainer.style.display = 'block';
        
        // Clear the form
        document.getElementById('caller-name').value = '';
        document.getElementById('call-notes').value = '';
        document.getElementById('call-duration').value = '0';
        document.getElementById('call-status').value = 'Completed';
        
        // Set default date/time to now
        const now = new Date();
        const dateTimeString = now.toISOString().slice(0, 16); // Format: YYYY-MM-DDTHH:MM
        document.getElementById('call-datetime').value = dateTimeString;
        
        // If editing, populate form with call data
        if (callData) {
            document.getElementById('caller-name').value = callData.CallerName || '';
            document.getElementById('call-notes').value = callData.CallNotes || '';
            document.getElementById('call-duration').value = callData.CallDuration || 0;
            document.getElementById('call-status').value = callData.CallStatus || 'Completed';
            
            if (callData.CallDateTime) {
                const callDate = new Date(callData.CallDateTime);
                const formattedDate = callDate.toISOString().slice(0, 16);
                document.getElementById('call-datetime').value = formattedDate;
            }
            
            // Store the call ID for updating
            document.getElementById('call-form').dataset.callId = callData.CallId;
        } else {
            // Remove any stored call ID
            delete document.getElementById('call-form').dataset.callId;
        }
    }

    hideCallForm() {
        const formContainer = document.querySelector('.call-form-container');
        if (formContainer) {
            formContainer.style.display = 'none';
        }
    }

    async saveCall() {
        const form = document.getElementById('call-form');
        const callId = form.dataset.callId;
        
        const callData = {
            candidateId: this.candidateId,
            callerName: document.getElementById('caller-name').value,
            callDateTime: document.getElementById('call-datetime').value,
            callDuration: parseInt(document.getElementById('call-duration').value),
            callNotes: document.getElementById('call-notes').value,
            callStatus: document.getElementById('call-status').value
        };
        
        try {
            let response;
            
            if (callId) {
                // Update existing call
                response = await fetch(`/api/phone-calls/${callId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(callData)
                });
            } else {
                // Create new call
                response = await fetch('/api/phone-calls', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(callData)
                });
            }
            
            if (!response.ok) {
                throw new Error(`Error saving call: ${response.statusText}`);
            }
            
            // Refresh the call list
            await this.loadPhoneCalls();
            this.hideCallForm();
            
        } catch (error) {
            console.error('Failed to save phone call:', error);
            alert('Failed to save the phone call. Please try again.');
        }
    }

    async editCall(callId) {
        const call = this.calls.find(c => c.CallId == callId);
        if (call) {
            this.showCallForm(call);
        }
    }

    async deleteCall(callId) {
        if (!confirm('Are you sure you want to delete this phone call record?')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/phone-calls/${callId}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                throw new Error(`Error deleting call: ${response.statusText}`);
            }
            
            // Refresh the call list
            await this.loadPhoneCalls();
            
        } catch (error) {
            console.error('Failed to delete phone call:', error);
            alert('Failed to delete the phone call. Please try again.');
        }
    }
}

// Initialize the Phone Call Manager when a candidate ID is available
document.addEventListener('DOMContentLoaded', () => {
    // The manager will be initialized when a candidate ID is available
    // This can come from different sources depending on your application structure
    
    // Option 1: Get from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const candidateId = urlParams.get('candidateId') || urlParams.get('id');
    
    // Option 2: Get from a hidden input field on the page
    const candidateIdInput = document.querySelector('input[name="candidateId"]');
    
    // Option 3: Get from a data attribute on an element
    const candidateElement = document.querySelector('[data-candidate-id]');
    
    let id = candidateId || 
             (candidateIdInput ? candidateIdInput.value : null) || 
             (candidateElement ? candidateElement.dataset.candidateId : null);
    
    if (id) {
        // Initialize the phone call manager with the candidate ID
        const phoneCallManager = new PhoneCallManager(id);
        phoneCallManager.init();
        
        // Make it globally accessible for debugging
        window.phoneCallManager = phoneCallManager;
    }
}); 